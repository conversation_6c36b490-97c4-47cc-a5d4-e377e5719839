variable "cluster_name" {
  type    = string
  default = "qr-ec2"
}

variable "namespace" {
  default = ""
}

variable "stage" {
  default = "qa"
}

variable "vpc_cidr" {
  type    = string
  default = "10.0.0.0/16"
}

variable "vpc_id" {
  type    = string
  default = "vpc-0dab4394884b33fb3"
}

variable "subnet_id" {
  type    = string
  default = "subnet-099f6d6a061d3a234"
}

variable "natgw_subnet" {
  type    = string
  default = "subnet-051a2ea48fd16d120"
}

variable "private_route_table" {
  type    = string
  default = "rtb-09bb93e1ac93f0995"
}

variable "azs" {
  default = ["us-east-2a", "us-east-2b", "us-east-2c"]
}

variable "public_subnet_cidrs" {
  default = ["13.0.101.0/24"]
}

variable "private_subnets" {
  default = []
}

variable "private_subnet_cidrs" {
  default = ["13.0.104.0/24"]
}

variable "oidc_thumbprint_list" {
  default = []
}

variable "iam_path" {
  default = "/"
}

variable "instance_types" {
  type    = list(string)
  default = ["t3.xlarge"]
}

variable "aws_sbox_access_key" {
  default = ""
}

variable "aws_sbox_secret_key" {
  default = ""
}

variable "docker_username" {
  default = ""
}

variable "docker_password" {
  default = ""
}

variable "infra_gitops_rsa" {
  default = ""
}

variable "http_port" {
  default = 31816
}

variable "https_port" {
  default = 31046
}