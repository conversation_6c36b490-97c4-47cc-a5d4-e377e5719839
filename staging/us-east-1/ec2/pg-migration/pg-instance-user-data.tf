locals {
  pg-backup-userdata = <<USERDATA
MIME-Version: 1.0
Content-Type: multipart/mixed; boundary="==UDBOUNDARY=="

--==UDBOUNDARY==
Content-Type: text/x-shellscript; charset="us-ascii"

#!/bin/bash -xe
echo "Running custom user data script" > /tmp/udata.txt
yum update -y
yum install -y amazon-ssm-agent yum-utils
systemctl enable amazon-ssm-agent && systemctl start amazon-ssm-agent
echo "ssm agent installed successfully" >> /tmp/udata.txt
yum install -y docker
systemctl enable --now docker.service
usermod -aG docker ec2-user
echo "installed docker" >> /tmp/udata.txt
yum groupinstall -y 'Development Tools'
yum install -y procps-ng curl file git
echo "installed dependencies" >> /tmp/udata.txt
wget https://go.dev/dl/go1.19.1.linux-amd64.tar.gz
rm -rf /usr/local/go && tar -C /usr/local -xzf go1.19.1.linux-amd64.tar.gz
echo "installed golang" >> /tmp/udata.txt
amazon-linux-extras install -y epel
yum repolist
tee /etc/yum.repos.d/pgdg.repo<<EOF
[pgdg14]
name=PostgreSQL 14 for RHEL/CentOS 7 - x86_64
baseurl=http://download.postgresql.org/pub/repos/yum/14/redhat/rhel-7-x86_64
enabled=1
gpgcheck=0
EOF
yum makecache
yum install -y postgresql14
date >> /tmp/udata.txt

--==UDBOUNDARY==--
USERDATA
}