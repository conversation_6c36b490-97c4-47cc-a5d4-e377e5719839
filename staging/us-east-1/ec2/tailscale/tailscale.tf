resource "aws_instance" "tscale" {
  ami                    = "ami-0bfac9aa66a558bd8"
  instance_type          = "t4g.medium"
  user_data              = local.tailscale-userdata
  subnet_id              = var.subnet_id
  iam_instance_profile   = aws_iam_instance_profile.tscale_profile.name
  vpc_security_group_ids = [aws_security_group.tscale_sg.id]
  key_name               = aws_key_pair.keypair.key_name

  tags = {
    Name = "qr-tailscale-router-ec2-staging"
  }
}

resource "aws_security_group" "tscale_sg" {
  name        = "qr-dev-sg-staging"
  description = "tailscale-staging security group"
  vpc_id      = var.vpc_id

  tags = {
    Name = "qr staging sg"
  }
}

# resource "aws_security_group_rule" "ssh" {
#   type              = "ingress"
#   from_port         = 22
#   to_port           = 22
#   protocol          = "tcp"
#   cidr_blocks       = ["0.0.0.0/0"]
#   security_group_id = aws_security_group.tscale_sg.id
# }

resource "aws_security_group_rule" "direct_connections" {
  type              = "ingress"
  description       = "Allow UDP direct connections"
  from_port         = 41641
  to_port           = 41641
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.tscale_sg.id
}

resource "aws_security_group_rule" "allow_all_egress" {
  type              = "egress"
  to_port           = 0
  protocol          = "-1"
  from_port         = 0
  security_group_id = aws_security_group.tscale_sg.id
  cidr_blocks       = ["0.0.0.0/0"]
}

resource "aws_security_group_rule" "allow_all_ingress" {
  type              = "ingress"
  to_port           = 5432
  protocol          = "tcp"
  from_port         = 5432
  security_group_id = "sg-062b6b32e12dd14b9"
  source_security_group_id = aws_security_group.tscale_sg.id
}

resource "aws_iam_instance_profile" "tscale_profile" {
  name = "tailscale_qa_profile"
  role = aws_iam_role.tscale.name
}

resource "aws_iam_role" "tscale" {
  name = "tailscale_qa_role"
  path = "/"

  assume_role_policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Action": "sts:AssumeRole",
            "Principal": {
               "Service": "ec2.amazonaws.com"
            },
            "Effect": "Allow",
            "Sid": ""
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "tscale_ssm-core-attach" {
  role       = aws_iam_role.tscale.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}
