# resource "aws_eip" "natgw" {
#   vpc = true
# }

# resource "aws_nat_gateway" "tailscale" {
#   allocation_id = aws_eip.natgw.id
#   subnet_id     = var.natgw_subnet

#   tags = {
#     Name = "QR Dev NAT GW"
#   }
# }

# resource "aws_route" "natgw" {
#   route_table_id         = var.private_route_table
#   destination_cidr_block = "0.0.0.0/0"
#   nat_gateway_id         = aws_nat_gateway.tailscale.id
# }