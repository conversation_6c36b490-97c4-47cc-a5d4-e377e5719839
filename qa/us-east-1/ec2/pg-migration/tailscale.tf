# resource "aws_instance" "tscale" {
#   ami                    = "ami-0b0dcb5067f052a63"
#   instance_type          = "t3.medium"
#   user_data              = local.tailscale-userdata
#   subnet_id              = var.subnet_id
#   iam_instance_profile   = aws_iam_instance_profile.tscale_profile.name
#   vpc_security_group_ids = [aws_security_group.tscale_sg.id]
#   key_name               = aws_key_pair.keypair.key_name

#   tags = {
#     Name = "tailscale-router-ec2-qa"
#   }
# }

# resource "aws_security_group" "tscale_sg" {
#   name        = "tailscale-qa"
#   description = "tailscale-qa security group"
#   vpc_id      = var.vpc_id

#   tags = {
#     Name = "tailscale qa sg"
#   }
# }

# resource "aws_security_group_rule" "ssh" {
#   type              = "ingress"
#   from_port         = 22
#   to_port           = 22
#   protocol          = "tcp"
#   cidr_blocks       = ["0.0.0.0/0"]
#   security_group_id = aws_security_group.tscale_sg.id
# }

# resource "aws_security_group_rule" "direct_connections" {
#   type              = "ingress"
#   description       = "Allow UDP direct connections"
#   from_port         = 41641
#   to_port           = 41641
#   protocol          = "tcp"
#   cidr_blocks       = ["0.0.0.0/0"]
#   security_group_id = aws_security_group.tscale_sg.id
# }

# resource "aws_security_group_rule" "allow_all" {
#   type              = "egress"
#   to_port           = 0
#   protocol          = "-1"
#   from_port         = 0
#   security_group_id = aws_security_group.tscale_sg.id
#   cidr_blocks       = ["0.0.0.0/0"]
# }

# resource "aws_iam_instance_profile" "tscale_profile" {
#   name = "tailscale_qa_profile"
#   role = aws_iam_role.tscale.name
# }

# resource "aws_iam_role" "tscale" {
#   name = "tailscale_qa_role"
#   path = "/"

#   assume_role_policy = <<EOF
# {
#     "Version": "2012-10-17",
#     "Statement": [
#         {
#             "Action": "sts:AssumeRole",
#             "Principal": {
#                "Service": "ec2.amazonaws.com"
#             },
#             "Effect": "Allow",
#             "Sid": ""
#         }
#     ]
# }
# EOF
# }

# resource "aws_iam_role_policy_attachment" "tscale_ssm-core-attach" {
#   role       = aws_iam_role.tscale.name
#   policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
# }