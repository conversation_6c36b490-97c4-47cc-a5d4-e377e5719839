locals {
  tailscale-userdata = <<USERDATA
MIME-Version: 1.0
Content-Type: multipart/mixed; boundary="==UDBOUNDARY=="

--==UDBOUNDARY==
Content-Type: text/x-shellscript; charset="us-ascii"

#!/bin/bash -xe
echo "Running custom user data script" > /tmp/udata.txt
hostnamectl set-hostname router-lp-ec2-qa-use1.localdomain
yum install -y amazon-ssm-agent yum-utils
echo "ssm agent installed successfully" >> /tmp/udata.txt
systemctl enable amazon-ssm-agent && systemctl start amazon-ssm-agent
echo 'net.ipv4.ip_forward = 1' | tee -a /etc/sysctl.conf
echo 'net.ipv6.conf.all.forwarding = 1' | tee -a /etc/sysctl.conf
sysctl -p /etc/sysctl.conf
yum-config-manager --add-repo https://pkgs.tailscale.com/stable/amazon-linux/2/tailscale.repo
yum install -y tailscale
systemctl enable --now tailscaled
tailscale up --authkey tskey-auth-kvoktj2CNTRL-KTgzxA3T939m8N71kMgM69x12bt1tvRc --advertise-routes=10.0.0.0/16 --accept-dns=false --accept-routes

date >> /tmp/udata.txt

--==UDBOUNDARY==--
USERDATA
}