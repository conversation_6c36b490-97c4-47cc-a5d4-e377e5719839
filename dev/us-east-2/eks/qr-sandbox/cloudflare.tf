resource "cloudflare_record" "rancher" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "rancher"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "acd" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "argocd"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "vault" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "vault"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "traefik" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "traefik"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "k8s_dashboard" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "eks"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "ceph_dashboard" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "rook"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "b64" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "b64"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "idm" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "idm"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "jhub" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "jupyterhub"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "awf" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "awf"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "kcost" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "kcost"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "pgadmin" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "pgadmin"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "nextcloud" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "nextcloud"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "wiki" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "wiki"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "blog" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "blog"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "grafana" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "grafana"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "atlantis" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "atlantis"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "kargo" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "kargo"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

resource "cloudflare_record" "notes" {
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = "notes"
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

# output "ld_account_id" {
#   value = data.cloudflare_zone.ldevops.account_id
# }

# output "ld_zone_id" {
#   value = data.cloudflare_zone.ldevops.id
# }
