# Default values for aws-efs-csi-driver.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

nameOverride: ""
fullnameOverride: ""

replicaCount: 2

useFIPS: false

image:
  repository: amazon/aws-efs-csi-driver
  tag: "v1.7.5"
  pullPolicy: IfNotPresent

sidecars:
  livenessProbe:
    image:
      repository: public.ecr.aws/eks-distro/kubernetes-csi/livenessprobe
      tag: v2.11.0-eks-1-29-2
      pullPolicy: IfNotPresent
    resources: {}
    securityContext:
      readOnlyRootFilesystem: true
      allowPrivilegeEscalation: false
  nodeDriverRegistrar:
    image:
      repository: public.ecr.aws/eks-distro/kubernetes-csi/node-driver-registrar
      tag: v2.9.3-eks-1-29-2
      pullPolicy: IfNotPresent
    resources: {}
    securityContext:
      readOnlyRootFilesystem: true
      allowPrivilegeEscalation: false
  csiProvisioner:
    image:
      repository: public.ecr.aws/eks-distro/kubernetes-csi/external-provisioner
      tag: v3.6.3-eks-1-29-2
      pullPolicy: IfNotPresent
    resources: {}
    securityContext:
      readOnlyRootFilesystem: true
      allowPrivilegeEscalation: false

imagePullSecrets:
  - name: docker-hub-credentials

## Controller deployment variables

controller:
  # Specifies whether a deployment should be created
  create: true
  # Number for the log level verbosity
  logLevel: 2
  # If set, add pv/pvc metadata to plugin create requests as parameters.
  extraCreateMetadata: true
  # Add additional tags to access points
  tags:
    {}
    # environment: prod
    # region: us-east-1
  # Enable if you want the controller to also delete the
  # path on efs when deleteing an access point
  deleteAccessPointRootDir: false
  podAnnotations: {}
  resources:
    {}
    # We usually recommend not to specify default resources and to leave this as a conscious
    # choice for the user. This also increases chances charts run on environments with little
    # resources, such as Minikube. If you do want to specify resources, uncomment the following
    # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
    # limits:
    #   cpu: 100m
    #   memory: 128Mi
    # requests:
    #   cpu: 100m
    #   memory: 128Mi
  nodeSelector: {}
  updateStrategy: {}
  tolerations:
    - key: CriticalAddonsOnly
      operator: Exists
    - key: efs.csi.aws.com/agent-not-ready
      operator: Exists
  affinity: {}
  # Specifies whether a service account should be created
  serviceAccount:
    create: true
    name: efs-csi-controller-sa
    annotations:
    ## Enable if EKS IAM for SA is used
     eks.amazonaws.com/role-arn: arn:aws:iam::************:role/qr-sandbox-dev-efs-role
  healthPort: 9909
  regionalStsEndpoints: false
  # securityContext on the controller pod
  securityContext:
    runAsNonRoot: false
    runAsUser: 0
    runAsGroup: 0
    fsGroup: 0


## Node daemonset variables

node:
  # Number for the log level verbosity
  logLevel: 2
  volMetricsOptIn: false
  volMetricsRefreshPeriod: 240
  volMetricsFsRateLimit: 5
  hostAliases:
    {}
    # For cross VPC EFS, you need to poison or overwrite the DNS for the efs volume as per
    # https://docs.aws.amazon.com/efs/latest/ug/efs-different-vpc.html#wt6-efs-utils-step3
    # implementing the suggested solution found here:
    # https://github.com/kubernetes-sigs/aws-efs-csi-driver/issues/240#issuecomment-*********
    # EFS Vol ID, IP, Region
    # "fs-********":
    #   ip: *********
    #   region: us-east-2
  dnsPolicy: ClusterFirst
  dnsConfig:
    {}
    # Example config which uses the AWS nameservers
    # dnsPolicy: "None"
    # dnsConfig:
    #   nameservers:
    #     - ***************
  podAnnotations: {}
  resources:
    {}
    # limits:
    #   cpu: 100m
    #   memory: 128Mi
    # requests:
    #   cpu: 100m
    #   memory: 128Mi
  nodeSelector: {}
  updateStrategy: {}
    # Override default strategy (RollingUpdate) to speed up deployment.
    # This can be useful if helm timeouts are observed.
    # type: OnDelete
  tolerations:
    - operator: Exists
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: eks.amazonaws.com/compute-type
                operator: NotIn
                values:
                  - fargate
  # Specifies whether a service account should be created
  serviceAccount:
    create: true
    name: efs-csi-node-sa
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/qr-sandbox-dev-efs-role
    ## Enable if EKS IAM for SA is used
    #  eks.amazonaws.com/role-arn: arn:aws:iam::************:role/efs-csi-role
  healthPort: 9809
  # securityContext on the node pod
  securityContext:
    # The node pod must be run as root to bind to the registration/driver sockets
    runAsNonRoot: false
    runAsUser: 0
    runAsGroup: 0
    fsGroup: 0

storageClasses: []
# Add StorageClass resources like:
# - name: efs-sc
#   annotations:
#     # Use that annotation if you want this to your default storageclass
#     storageclass.kubernetes.io/is-default-class: "true"
#   mountOptions:
#   - tls
#   parameters:
#     provisioningMode: efs-ap
#     fileSystemId: fs-1122aabb
#     directoryPerms: "700"
#     gidRangeStart: "1000"
#     gidRangeEnd: "2000"
#     basePath: "/dynamic_provisioning"
#     subPathPattern: "/subPath"
#     ensureUniqueDirectory: true
#   reclaimPolicy: Delete
#   volumeBindingMode: Immediate

