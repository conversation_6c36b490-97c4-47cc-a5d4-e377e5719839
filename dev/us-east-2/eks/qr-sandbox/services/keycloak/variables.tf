variable "cluster_name" {
  type    = string
  default = "${var.cluster_name}"
}

variable "stage" {
  default = "sandbox"
}

variable "vpc_cidr" {
  type    = string
  default = "12.0.0.0/16"
}

variable "azs" {
  default = ["us-west-2a", "us-west-2b", "us-west-2c"]
}

variable "public_subnet_cidrs" {
  default = ["12.0.101.0/24", "12.0.102.0/24", "12.0.103.0/24"]
}

variable "private_subnets" {
  default = []
}

variable "private_subnet_cidrs" {
  default = ["12.0.104.0/24", "12.0.105.0/24", "12.0.106.0/24"]
}

variable "lb_arn" {
  type    = string
  default = "arn:aws:elasticloadbalancing:us-west-2:172158540696:loadbalancer/net/k8s-mgmt-eks-nlb/22e574e79b092cb7"
}

variable "lb_name" {
  type    = string
  default = "k8s-mgmt-eks-nlb"
}