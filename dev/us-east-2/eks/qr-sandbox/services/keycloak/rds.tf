resource "aws_db_instance" "keycloak" {
  allocated_storage    = 10
  engine               = "postgres"
  engine_version       = "13.1"
  instance_class       = "db.t3.micro"
  name                 = "keycloak"
  identifier           = "keycloak"
  username             = "keycloak"
  password             = "Kc923841"
  db_subnet_group_name = "k8s-sng"
  availability_zone    = "us-west-2a"
  skip_final_snapshot  = true
}