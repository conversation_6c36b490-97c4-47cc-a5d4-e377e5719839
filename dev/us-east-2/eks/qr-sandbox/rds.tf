# resource "aws_db_parameter_group" "qr_sbox" {
#   name   = "qr-sbox-pg"
#   family = "postgres14"

#   parameter {
#     name  = "log_connections"
#     value = "1"
#   }

#   lifecycle {
#     create_before_destroy = true
#   }
# }

# resource "aws_db_subnet_group" "qr_sbox" {
#   name       = "qr_sbox"
#   subnet_ids = module.vpc.private_subnets

#   tags = {
#     Name = "QR Sandbox DB subnet group"
#   }
# }

# resource "aws_db_instance" "qr_sbox" {
#   identifier                          = "qr-sbox"
#   allocated_storage                   = 10
#   db_name                             = "qr_sbox"
#   engine                              = "postgres"
#   engine_version                      = "14"
#   instance_class                      = "db.t3.large"
#   username                            = "postgres"
#   password                            = "Passwd12"
#   availability_zone                   = "us-east-2a"
#   db_subnet_group_name                = aws_db_subnet_group.qr_sbox.name
#   vpc_security_group_ids              = [module.eks.worker_security_group_id]
#   parameter_group_name                = aws_db_parameter_group.qr_sbox.name
#   iam_database_authentication_enabled = true
#   skip_final_snapshot                 = true
#   snapshot_identifier                 = "qr-sbox-12122023"
# }