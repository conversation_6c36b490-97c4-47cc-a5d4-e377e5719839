# resource "aws_acm_certificate" "qwr" {
#   domain_name       = "*.qwrobins.net"
#   validation_method = "DNS"
# }

# resource "cloudflare_record" "val_record" {
#   for_each = {
#     for item in aws_acm_certificate.qwr.domain_validation_options : item.domain_name => {
#       name   = item.resource_record_name
#       record = item.resource_record_value
#       type   = item.resource_record_type
#     }
#   }

#   zone_id         = data.cloudflare_zone.qwrnet.id
#   allow_overwrite = true
#   proxied         = false
#   name            = each.value.name
#   type            = each.value.type
#   value           = each.value.record
#   ttl             = 60
# }

# resource "aws_acm_certificate_validation" "qwr_validation" {
#   certificate_arn         = aws_acm_certificate.qwr.arn
#   validation_record_fqdns = [for record in cloudflare_record.val_record : record.hostname]
# }