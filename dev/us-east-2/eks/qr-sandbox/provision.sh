#!/bin/bash
terraform apply -auto-approve
aws eks update-kubeconfig --name qr-sandbox-dev --alias qr-dev --region us-east-2
kubectl create ns argocd
kubectl apply -f bootstrap/docker-hub-credentials.yaml
kubectl apply -f bootstrap/argocd-install.yaml -n argocd

# Loop until all pods in argocd namespace are ready
while [[ $(kubectl get pods -n argocd --field-selector=status.phase!=Running,status.phase!=Succeeded | grep -v "STATUS\|Running\|Completed" | grep -c " 1/1 ") -ne $(kubectl get pods -n argocd --field-selector=status.phase!=Running,status.phase!=Succeeded | grep -v "STATUS\|Running\|Completed" | wc -l) ]]; do
  echo "Waiting for all pods in argocd namespace to be ready..."
  sleep 15
done
echo "All pods in the argocd namespace are ready! Applying gitops configuration"
kubectl apply -n argocd -f bootstrap/argocd-install.yaml -n argocd
kubectl apply -k bootstrap
kubectl delete pod -l app.kubernetes.io/name=argocd-dex-server -n argocd
kubectl delete pod -l app.kubernetes.io/name=argocd-server -n argocd
