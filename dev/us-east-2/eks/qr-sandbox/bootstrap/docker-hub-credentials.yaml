apiVersion: v1
kind: Secret
metadata:
  name: docker-hub-credentials
  namespace: argocd
  annotations:
    reflector.v1.k8s.emberstack.com/reflection-allowed: "true"
    reflector.v1.k8s.emberstack.com/reflection-auto-enabled: "true"
    reflector.v1.k8s.emberstack.com/reflection-auto-namespaces: "argo,argo-events,b64,kargo,vault,cattle-system,kubernetes-dashboard,monitoring,pgadmin,utils"
data:
  .dockerconfigjson: ewoJImF1dGhzIjogewoJCSJodHRwczovL2luZGV4LmRvY2tlci5pby92MS8iOiB7CgkJCSJhdXRoIjogImNYZHliMkpwYm5NNlNrQmphMmhoYlcxbGNqYzMiCgkJfSwKCQkicXVheS5pbyI6IHsKCQkJImF1dGgiOiAiY1hkeWIySnBibk02WTNWME1YWmxlalp0ZW5nM1NGWllRR2hqZWc9PSIKCQl9Cgl9Cn0=
type: kubernetes.io/dockerconfigjson
