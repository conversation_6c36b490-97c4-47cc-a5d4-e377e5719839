apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: gp3
  annotations:
    storageclass.kubernetes.io/is-default-class: "true"  # Optional: Set as default
provisioner: ebs.csi.aws.com
volumeBindingMode: WaitForFirstConsumer
parameters:
  type: gp3
  fsType: ext4
  encrypted: "true"  # AWS managed encryption (SSE-S3)
  # Optional performance parameters:
  iops: "3000"
  throughput: "125" 
  # iopsPerGB: "50"  # Alternative to fixed iops
allowVolumeExpansion: true
reclaimPolicy: Delete