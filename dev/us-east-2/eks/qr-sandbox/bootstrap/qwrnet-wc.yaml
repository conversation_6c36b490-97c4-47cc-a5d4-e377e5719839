apiVersion: v1
data:
  tls.crt: 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
  tls.key: 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
kind: Secret
metadata:
  name: qwrnet-bundle-tls
  namespace: kube-system
  annotations:
    reflector.v1.k8s.emberstack.com/reflection-allowed: "true"
    reflector.v1.k8s.emberstack.com/reflection-auto-enabled: "true"
    reflector.v1.k8s.emberstack.com/reflection-allowed-namespaces: "argo,argo-events,b64,vault,cattle-system,kubernetes-dashboard,kargo,nextcloud,pgadmin,wikijs,monitoring,atlantis,harbor,notes"
    reflector.v1.k8s.emberstack.com/reflection-auto-namespaces: "argo,argo-events,b64,vault,cattle-system,kubernetes-dashboard,kargo,nextcloud,pgadmin,wikijs,monitoring,atlantis,harbor,notes"
type: kubernetes.io/tls
