# resource "helm_release" "kube-prometheus-stack" {
#   namespace        = "monitoring"
#   create_namespace = true
#   depends_on = [
#     module.eks
#     # module.eks.module.node_groups.aws_eks_node_group.workers["spot-xlarge"]
#   ]

#   name       = "kube-prometheus-stack"
#   repository = "https://prometheus-community.github.io/helm-charts"
#   chart      = "kube-prometheus-stack"
#   version    = "56.8.0"

#   values = [
#     "${file("services/prometheus/values.yaml")}"
#   ]
# }