#!/bin/bash

aws eks update-kubeconfig --name qr-sandbox-dev --alias qr-dev --region us-east-2
terraform destroy -auto-approve -target=module.eks

# Set your region
REGION='us-east-2'

# List all 'available' volumes in the specified region and delete them one by one
VOLUME_IDS=$(aws ec2 describe-volumes --region $REGION --query "Volumes[?State=='available'].VolumeId[]" --output text)

for VOLUME_ID in $VOLUME_IDS; do
    echo "Deleting volume: $VOLUME_ID"
    aws ec2 delete-volume --volume-id $VOLUME_ID --region $REGION
done
