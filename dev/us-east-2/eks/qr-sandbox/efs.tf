# resource "aws_efs_file_system" "qr_sbox_efs" {
#   creation_token   = "qr-sbox-efs"
#   performance_mode = "generalPurpose"
#   encrypted        = true
#   kms_key_id       = aws_kms_key.ebs_encryption.arn

#   tags = {
#     Name = "qr-sbox-efs"
#   }

#   depends_on = [
#     aws_kms_key.ebs_encryption,
#     module.eks
#   ]
# }

# resource "aws_efs_access_point" "qr_sbox_efs_ap" {
#   file_system_id = aws_efs_file_system.qr_sbox_efs.id

#   posix_user {
#     gid = 33
#     uid = 33
#   }

#   root_directory {
#     path = "/"
#     creation_info {
#       owner_gid   = 33
#       owner_uid   = 33
#       permissions = "777"
#     }
#   }

#   tags = {
#     Name = "qr-sbox-EFSAccessPoint"
#   }

#   depends_on = [
#     aws_efs_file_system.qr_sbox_efs,
#     module.eks
#   ]
# }

# resource "aws_efs_mount_target" "qr_sbox_efs_mt" {
#   for_each        = { for subnet_id in module.vpc.private_subnets : subnet_id => subnet_id }
#   file_system_id  = aws_efs_file_system.qr_sbox_efs.id
#   subnet_id       = each.value
#   security_groups = [module.eks.worker_security_group_id, module.eks.cluster_primary_security_group_id, module.eks.cluster_security_group_id]

#   depends_on = [
#     aws_efs_file_system.qr_sbox_efs,
#     module.eks
#   ]
# }

# # Allow NFS traffic to EKS Worker Nodes from EFS
# resource "aws_security_group_rule" "eks_worker_efs_nfs" {
#   type              = "ingress"
#   from_port         = 2049
#   to_port           = 2049
#   protocol          = "tcp"
#   security_group_id = module.eks.worker_security_group_id
#   cidr_blocks       = ["********/16"] # Ideally, restrict this to the VPC CIDR or specific IP ranges
# }

# # Allow NFS traffic to EKS Cluster Primary Security Group from EFS
# resource "aws_security_group_rule" "eks_primary_sg_efs_nfs" {
#   type              = "ingress"
#   from_port         = 2049
#   to_port           = 2049
#   protocol          = "tcp"
#   security_group_id = module.eks.cluster_primary_security_group_id
#   cidr_blocks       = ["********/16"] # Ideally, restrict this to the VPC CIDR or specific IP ranges
# }

# # Optional: If EFS needs to be accessible by the cluster security group as well
# resource "aws_security_group_rule" "eks_cluster_sg_efs_nfs" {
#   type              = "ingress"
#   from_port         = 2049
#   to_port           = 2049
#   protocol          = "tcp"
#   security_group_id = module.eks.cluster_security_group_id
#   cidr_blocks       = ["********/16"] # Ideally, restrict this to the VPC CIDR or specific IP ranges
# }

# resource "helm_release" "efs_csi_driver" {
#   name       = "aws-efs-csi-driver"
#   repository = "https://kubernetes-sigs.github.io/aws-efs-csi-driver/"
#   chart      = "aws-efs-csi-driver"
#   version    = "2.5.5"
#   namespace  = "kube-system"

#   values = [
#     "${file("services/efs/efs-values.yaml")}"
#   ]
#   depends_on = [ module.eks ]
# }

# resource "kubernetes_manifest" "efs_sc" {
#   manifest = {
#     apiVersion = "storage.k8s.io/v1"
#     kind       = "StorageClass"
#     metadata = {
#       name = "efs-sc"
#     }
#     provisioner = "efs.csi.aws.com"
#   }
#   depends_on = [
#     helm_release.efs_csi_driver, module.eks
#   ]
# }

# resource "kubernetes_manifest" "efs_pv_nextcloud" {
#   manifest = {
#     apiVersion = "v1"
#     kind       = "PersistentVolume"
#     metadata = {
#       name = "efs-pv-nextcloud"
#     }
#     spec = {
#       capacity = {
#         storage = "1Ti"
#       }
#       volumeMode = "Filesystem"
#       accessModes = [
#         "ReadWriteMany"
#       ]
#       storageClassName = "efs-sc"
#       persistentVolumeReclaimPolicy = "Retain"
#       csi = {
#         driver = "efs.csi.aws.com"
#         volumeHandle = aws_efs_file_system.qr_sbox_efs.id
#       }
#     }
#   }
#   depends_on = [
#     kubernetes_manifest.efs_sc,
#     module.eks
#     ]
# }

# resource "kubernetes_manifest" "efs_pv_utils" {
#   manifest = {
#     apiVersion = "v1"
#     kind       = "PersistentVolume"
#     metadata = {
#       name = "efs-pv-utils"
#     }
#     spec = {
#       capacity = {
#         storage = "10Gi"
#       }
#       volumeMode = "Filesystem"
#       accessModes = [
#         "ReadWriteMany"
#       ]
#       storageClassName = "efs-sc"
#       persistentVolumeReclaimPolicy = "Retain"
#       csi = {
#         driver = "efs.csi.aws.com"
#         volumeHandle = aws_efs_file_system.qr_sbox_efs.id
#       }
#     }
#   }
#   depends_on = [
#     kubernetes_manifest.efs_sc,
#     module.eks
#   ]
# }
