# resource "aws_instance" "tscale" {
#   ami                    = "ami-0fa49cc9dc8d62c84"
#   instance_type          = "t3.medium"
#   user_data              = local.tailscale-userdata
#   subnet_id              = module.vpc.private_subnets[0]
#   iam_instance_profile   = aws_iam_instance_profile.tscale_profile.name
#   vpc_security_group_ids = [aws_security_group.tscale_sg.id]
#   key_name               = aws_key_pair.keypair.key_name

#   tags = {
#     Name = "tailscale-router-qr-eks-dev"
#   }

#   depends_on = [
#     module.vpc
#   ]
# }

# resource "aws_security_group" "tscale_sg" {
#   name        = "tailscale"
#   description = "tailscale security group"
#   vpc_id      = module.vpc.vpc_id

#   tags = {
#     Name = "tailscale sg"
#   }
# }

# resource "aws_security_group_rule" "ssh" {
#   type              = "ingress"
#   from_port         = 22
#   to_port           = 22
#   protocol          = "tcp"
#   cidr_blocks       = ["0.0.0.0/0"]
#   security_group_id = aws_security_group.tscale_sg.id
# }

# resource "aws_security_group_rule" "direct_connections" {
#   type              = "ingress"
#   description       = "Allow UDP direct connections"
#   from_port         = 41641
#   to_port           = 41641
#   protocol          = "tcp"
#   cidr_blocks       = ["0.0.0.0/0"]
#   security_group_id = aws_security_group.tscale_sg.id
# }

# resource "aws_security_group_rule" "allow_all" {
#   type              = "egress"
#   to_port           = 0
#   protocol          = "-1"
#   from_port         = 0
#   security_group_id = aws_security_group.tscale_sg.id
#   cidr_blocks       = ["0.0.0.0/0"]
# }

# resource "aws_iam_instance_profile" "tscale_profile" {
#   name = "tailscale_profile"
#   role = aws_iam_role.tscale.name
# }

# resource "aws_iam_role" "tscale" {
#   name = "tailscale_role"
#   path = "/"

#   assume_role_policy = <<EOF
# {
#     "Version": "2012-10-17",
#     "Statement": [
#         {
#             "Action": "sts:AssumeRole",
#             "Principal": {
#                "Service": "ec2.amazonaws.com"
#             },
#             "Effect": "Allow",
#             "Sid": ""
#         }
#     ]
# }
# EOF
# }

# resource "aws_iam_role_policy_attachment" "tscale_ssm-core-attach" {
#   role       = aws_iam_role.tscale.name
#   policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
# }