# module "eks_managed_node_group" {
#   source = "terraform-aws-modules/eks/aws//modules/eks-managed-node-group"

#   name            = "qr-dev-eks-second-ng"
#   cluster_name    = module.eks.cluster_id
#   cluster_version = module.eks.cluster_version

#   subnet_ids = module.vpc.private_subnets

#   // The following variables are necessary if you decide to use the module outside of the parent EKS module context.
#   // Without it, the security groups of the nodes are empty and thus won't join the cluster.
#   cluster_primary_security_group_id = module.eks.cluster_primary_security_group_id
#   vpc_security_group_ids            = [module.eks.cluster_security_group_id]

#   // Note: `disk_size`, and `remote_access` can only be set when using the EKS managed node group default launch template
#   // This module defaults to providing a custom launch template to allow for custom security groups, tag propagation, etc.
#   // use_custom_launch_template = false
#   // disk_size = 50
#   //
#   //  # Remote access cannot be specified with a launch template
#   //  remote_access = {
#   //    ec2_ssh_key               = module.key_pair.key_pair_name
#   //    source_security_group_ids = [aws_security_group.remote_access.id]
#   //  }

#   launch_template_id      = aws_launch_template.lt.id
#   launch_template_version = aws_launch_template.lt.default_version

#   min_size     = 1
#   max_size     = 2
#   desired_size = 1

#   instance_types = ["t3.xlarge"]
#   capacity_type  = "SPOT"

#   labels = {
#     Environment = "test"
#     GithubRepo  = "terraform-aws-eks"
#     GithubOrg   = "terraform-aws-modules"
#   }

#   taints = {
#     dedicated = {
#       key    = "dedicated"
#       value  = "secondNodeGroup"
#       effect = "NO_SCHEDULE"
#     }
#   }

#   tags = {
#     Environment = "dev"
#     Terraform   = "true"
#     Name = "${var.cluster_name}-2ng"
#   }
# }