# resource "helm_release" "karpenter" {
#   namespace        = "karpenter"
#   create_namespace = true
#   depends_on = [
#     module.eks.kubeconfig,
#     # helm_release.cilium
#   ]

#   name       = "karpenter"
#   repository = "https://charts.karpenter.sh"
#   chart      = "karpenter"
#   version    = "v0.5.3"

#   set {
#     name  = "serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
#     value = module.iam_assumable_role_karpenter.iam_role_arn
#   }

#   set {
#     name  = "controller.clusterName"
#     value = module.eks.cluster_id
#   }

#   set {
#     name  = "controller.clusterEndpoint"
#     value = module.eks.cluster_endpoint
#   }

# }

# resource "kubectl_manifest" "karpenter_provisioner" {
#   yaml_body = <<-YAML
#   apiVersion: karpenter.sh/v1alpha5
#   kind: Provisioner
#   metadata:
#     name: default
#   spec:
#     requirements:
#       - key: karpenter.sh/capacity-type
#         operator: In
#         values: ["spot"]
#     limits:
#       resources:
#         cpu: 1000
#     provider:
#       instanceProfile: "KarpenterNodeInstanceProfile-${var.cluster_name}"
#       subnetSelector:
#         karpenter.sh/discovery: ${module.eks.cluster_id}
#       securityGroupSelector:
#         karpenter.sh/discovery: qr-mgmt-sandbox
#       tags:
#         karpenter.sh/discovery: ${module.eks.cluster_id}
#     ttlSecondsAfterEmpty: 30
#   YAML

#   depends_on = [
#     helm_release.karpenter
#   ]
# }

# resource "kubectl_manifest" "karpenter_example_deployment" {
#   yaml_body = <<-YAML
#   apiVersion: apps/v1
#   kind: Deployment
#   metadata:
#     name: inflate
#   spec:
#     replicas: 0
#     selector:
#       matchLabels:
#         app: inflate
#     template:
#       metadata:
#         labels:
#           app: inflate
#       spec:
#         terminationGracePeriodSeconds: 0
#         containers:
#           - name: inflate
#             image: public.ecr.aws/eks-distro/kubernetes/pause:3.2
#             resources:
#               requests:
#                 cpu: 1
#   YAML

#   depends_on = [
#     helm_release.karpenter
#   ]
# }
