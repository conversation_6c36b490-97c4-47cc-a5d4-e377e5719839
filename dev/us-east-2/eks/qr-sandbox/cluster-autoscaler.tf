# resource "kubernetes_service_account" "autoscaler" {
#   metadata {
#     name      = "cluster-autoscaler"
#     namespace = "kube-system"
#     labels = {
#       k8s-addon = "cluster-autoscaler.adodns.k8s.io"
#       k8s-app   = "cluster-autoscaler"
#     }

#     annotations = {
#       "eks.amazonaws.com/role-arn" = module.eks.cluster_iam_role_arn
#     }

#   }
# }

# resource "helm_release" "autoscaler" {
#   name       = "autoscaler"
#   repository = "https://kubernetes.github.io/autoscaler"
#   chart      = "cluster-autoscaler"
#   namespace  = "kube-system"
#   version    = "9.21.0"
#   depends_on = [
#     module.eks
#   ]

#   set {
#     name  = "autoDiscovery.enabled"
#     value = "true"
#   }

#   set {
#     name  = "autoDiscovery.clusterName"
#     value = module.eks.cluster_id
#   }

#   set {
#     name  = "cloudProvider"
#     value = "aws"
#   }

#   set {
#     name  = "awsRegion"
#     value = "us-east-2"
#   }
# }
