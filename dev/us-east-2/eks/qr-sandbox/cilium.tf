# resource "helm_release" "cilium" {
#   name             = "cilium"
#   repository       = "https://helm.cilium.io/"
#   chart            = "cilium"
#   namespace        = "kube-system"
#   create_namespace = true
#   depends_on = [
#     null_resource.rm_aws_node,
#     module.vpc,
#     module.eks
#   ]

#   set {
#     name  = "hostname"
#     value = "cilium.mgmt.sandbox.lp.us"
#   }

#   set {
#     name  = "eni.enabled"
#     value = "true"
#   }

#   set {
#     name  = "ipam.mode"
#     value = "eni"
#   }

#   set {
#     name  = "egressMasqueradeInterfaces"
#     value = "eth0"
#   }

#   set {
#     name  = "tunnel"
#     value = "disabled"
#   }

#   set {
#     name  = "nodeinit.enabled"
#     value = "true"
#   }

#   set {
#     name  = "hubble.listenAddress"
#     value = ":4244"
#   }

#   set {
#     name  = "hubble.relay.enabled"
#     value = "true"
#   }

#   set {
#     name  = "hubble.ui.enabled"
#     value = "true"
#   }
# }

# helm upgrade --install cilium cilium/cilium --version 1.10.7 --namespace kube-system --set eni.enabled=true --set ipam.mode=eni --set egressMasqueradeInterfaces=eth0 --set tunnel=disabled --set nodeinit.enabled=true --set hubble.listenAddress=":4244" --set hubble.relay.enabled=true --set hubble.ui.enabled=true
