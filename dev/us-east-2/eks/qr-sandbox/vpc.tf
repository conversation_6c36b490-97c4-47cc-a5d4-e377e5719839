module "vpc" {
  source = "terraform-aws-modules/vpc/aws"

  name = var.cluster_name

  cidr = var.vpc_cidr

  azs                     = var.azs
  private_subnets         = var.private_subnet_cidrs
  public_subnets          = var.public_subnet_cidrs
  map_public_ip_on_launch = true

  enable_ipv6 = false

  enable_nat_gateway                     = true
  single_nat_gateway                     = true
  enable_dns_hostnames                   = true
  enable_dns_support                     = true
  create_database_subnet_group           = true
  create_database_subnet_route_table     = true
  create_database_internet_gateway_route = false

  public_subnet_tags = {
    Name                                   = "${var.cluster_name}-public"
    "kubernetes.io/role/elb"               = 1
    "kubernetes.io/cluster/qr-sandbox-dev" = "shared"
  }

  private_subnet_tags = {
    Name                                   = "${var.cluster_name}-private"
    "kubernetes.io/cluster/qr-sandbox-dev" = "shared"
    "kubernetes.io/role/internal-elb"      = 1
    "karpenter.sh/discovery"               = "${var.cluster_name}-${var.stage}"
  }

  tags = {
    Owner       = "QR"
    Environment = "dev"
  }

  vpc_tags = {
    Name = "${var.cluster_name}-vpc"
  }
}
