# resource "aws_customer_gateway" "s2s" {
#   bgp_asn     = 65000
#   ip_address  = "***********"
#   type        = "ipsec.1"
#   device_name = "UDM-Pro"

#   tags = {
#     Name = "s2s"
#   }
# }


# resource "aws_vpn_gateway" "vpn_gw" {
#   vpc_id = module.vpc.vpc_id

#   tags = {
#     Name = "s2s"
#   }
# }

# resource "aws_vpn_connection" "s2s" {
#   vpn_gateway_id           = aws_vpn_gateway.vpn_gw.id
#   customer_gateway_id      = aws_customer_gateway.s2s.id
#   type                     = "ipsec.1"
#   static_routes_only       = true
#   tunnel_inside_ip_version = "ipv4"
# }

# resource "aws_vpn_connection_route" "s2s" {
#   destination_cidr_block = "***********/16"
#   vpn_connection_id      = aws_vpn_connection.s2s.id
# }

# resource "aws_vpn_gateway_route_propagation" "s2s_rt_1" {
#   vpn_gateway_id = aws_vpn_gateway.vpn_gw.id
#   route_table_id = module.vpc.private_route_table_ids[0]
# }

# resource "aws_security_group_rule" "s2s" {
#   type              = "ingress"
#   from_port         = 0
#   to_port           = 0
#   protocol          = "all"
#   cidr_blocks       = ["***********/16"]
#   security_group_id = module.vpc.default_security_group_id
# }
