resource "aws_lb" "k8s_lb" {
  name               = "${var.cluster_name}-eks-nlb"
  internal           = false
  load_balancer_type = "network"
  subnets            = module.vpc.public_subnets

  enable_deletion_protection       = false
  enable_cross_zone_load_balancing = true

  tags = {
    environment = "sandbox"
  }
}

resource "aws_lb_target_group" "k8s_mgmt_tg_http" {
  name     = "${aws_lb.k8s_lb.name}-tg-http"
  port     = var.http_port
  protocol = "TCP"
  vpc_id   = module.vpc.vpc_id
}

resource "aws_lb_target_group" "k8s_mgmt_tg_https" {
  name     = "${aws_lb.k8s_lb.name}-tg-https"
  port     = var.https_port
  protocol = "TCP"
  vpc_id   = module.vpc.vpc_id
}

resource "aws_lb_listener" "http" {
  load_balancer_arn = aws_lb.k8s_lb.arn
  port              = "80"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.k8s_mgmt_tg_http.arn
  }
}

resource "aws_lb_listener" "https" {
  load_balancer_arn = aws_lb.k8s_lb.arn
  port              = "443"
  protocol          = "TCP"
  # certificate_arn   = aws_acm_certificate.ldevops.arn
  # alpn_policy       = "HTTP2Preferred"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.k8s_mgmt_tg_https.arn
  }
}

resource "aws_security_group_rule" "lb_http_csg" {
  type              = "ingress"
  from_port         = var.http_port
  to_port           = var.http_port
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = module.eks.cluster_security_group_id
}

resource "aws_security_group_rule" "lb_http_pcsg" {
  type              = "ingress"
  from_port         = var.http_port
  to_port           = var.http_port
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = module.eks.cluster_primary_security_group_id
}

resource "aws_security_group_rule" "lb_https_csg" {
  type              = "ingress"
  from_port         = var.https_port
  to_port           = var.https_port
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = module.eks.cluster_security_group_id
}

resource "aws_security_group_rule" "lb_https_pcsg" {
  type              = "ingress"
  from_port         = var.https_port
  to_port           = var.https_port
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = module.eks.cluster_primary_security_group_id
}

resource "aws_security_group_rule" "lb_http" {
  type              = "ingress"
  from_port         = var.http_port
  to_port           = var.http_port
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = module.eks.worker_security_group_id
}

resource "aws_security_group_rule" "lb_https" {
  type              = "ingress"
  from_port         = var.https_port
  to_port           = var.https_port
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = module.eks.worker_security_group_id
}

resource "aws_autoscaling_attachment" "tg_http_blue" {
  autoscaling_group_name = module.eks.node_groups["spot-xlarge"].resources[0]["autoscaling_groups"][0]["name"]
  lb_target_group_arn    = aws_lb_target_group.k8s_mgmt_tg_http.arn
}

resource "aws_autoscaling_attachment" "tg_https_blue" {
  autoscaling_group_name = module.eks.node_groups["spot-xlarge"].resources[0]["autoscaling_groups"][0]["name"]
  lb_target_group_arn    = aws_lb_target_group.k8s_mgmt_tg_https.arn
}

# resource "aws_autoscaling_attachment" "tg_http_green" {
#   autoscaling_group_name = module.eks.node_groups["mgmt-green"].resources[0]["autoscaling_groups"][0]["name"]
#   lb_target_group_arn = aws_lb_target_group.k8s_mgmt_tg_http.arn
# }

# resource "aws_autoscaling_attachment" "tg_https_green" {
#   autoscaling_group_name = module.eks.node_groups["mgmt-green"].resources[0]["autoscaling_groups"][0]["name"]
#   lb_target_group_arn = aws_lb_target_group.k8s_mgmt_tg_https.arn
# }
