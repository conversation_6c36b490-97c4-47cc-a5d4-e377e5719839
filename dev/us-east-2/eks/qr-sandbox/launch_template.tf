resource "aws_launch_template" "lt" {
  name_prefix            = "${var.cluster_name}-"
  description            = "${var.cluster_name} Launch-Template"
  update_default_version = true
  key_name               = aws_key_pair.keypair.key_name

  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      volume_size           = 50
      volume_type           = "gp3"
      delete_on_termination = true
      encrypted             = true
      # kms_key_id            = aws_kms_key.ebs_encryption.arn
    }
  }

  # block_device_mappings {
  #   device_name = "/dev/xvdb"

  #   ebs {
  #     volume_size           = 120
  #     volume_type           = "gp3"
  #     delete_on_termination = true
  #     encrypted             = true
  #     # kms_key_id            = aws_kms_key.ebs_encryption.arn
  #   }
  # }

  monitoring {
    enabled = true
  }

  network_interfaces {
    associate_public_ip_address = false
    delete_on_termination       = true
    security_groups             = [module.eks.worker_security_group_id]
  }

  user_data = base64encode(
    local.eks-node-private-userdata,
  )


  tag_specifications {
    resource_type = "instance"

    tags = {
      Cluster = "${var.cluster_name}"
      Name    = "${module.eks.cluster_id}-node"
    }
  }

  tag_specifications {
    resource_type = "volume"

    tags = {
      Cluster = "${module.eks.cluster_id}"
    }
  }

  tag_specifications {
    resource_type = "network-interface"

    tags = {
      Cluster = "${module.eks.cluster_id}"
    }
  }

  # Tag the LT itself
  tags = {
    Cluster = "${module.eks.cluster_id}"
  }

  lifecycle {
    create_before_destroy = true
  }
}