# if you have used ASGs before, that role got auto-created already and you need to import to TF state
resource "aws_iam_service_linked_role" "autoscaling" {
  aws_service_name = "autoscaling.amazonaws.com"
  description      = "Default Service-Linked Role enables access to AWS Services and Resources used or managed by Auto Scaling"
  custom_suffix    = "lt_with_managed_node_groups" # the full name is "AWSServiceRoleForAutoScaling_lt_with_managed_node_groups" < 64 characters
}

# data "aws_caller_identity" "current" {}

# This policy is required for the KMS key used for EKS root volumes, so the cluster is allowed to enc/dec/attach encrypted EBS volumes
data "aws_iam_policy_document" "ebs_decryption" {
  statement {
    #  sid    = "EnableIAMUserPermissions"
    effect = "Allow"

    #  principals {
    #    type        = "AWS"
    #    identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    #  }

    actions = [
      "kms:*"
    ]

    resources = ["*"]
  }

  # Required for EKS
  statement {
    #  sid    = "Allowservice-linkedroleuseoftheCMK"
    effect = "Allow"

    #  principals {
    #    type = "AWS"
    #    identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling", module.eks.cluster_iam_role_arn]
    #  }

    actions = [
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
      "kms:DescribeKey"
    ]

    resources = ["*"]
  }

  statement {
    #  sid    = "Allowattachmentofpersistentresources"
    effect = "Allow"

    #  principals {
    #    type = "AWS"
    #    identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling", module.eks.cluster_iam_role_arn]
    #  }

    actions = [
      "kms:CreateGrant"
    ]

    resources = ["*"]

    condition {
      test     = "Bool"
      variable = "kms:GrantIsForAWSResource"
      values   = ["true"]
    }

  }
}
