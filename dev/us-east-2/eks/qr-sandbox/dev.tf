# resource "aws_instance" "dev" {
#   ami                    = "ami-0fa49cc9dc8d62c84"
#   instance_type          = "t3.xlarge"
#   user_data              = local.dev-userdata
#   subnet_id              = module.vpc.private_subnets[0]
#   iam_instance_profile   = aws_iam_instance_profile.qr_ec2_dev_profile.name
#   vpc_security_group_ids = [aws_security_group.qr_ec2_dev_sg.id]
#   key_name               = aws_key_pair.keypair.key_name

#   root_block_device {
#     # encrypted = true
#     # kms_key_id = aws_kms_key.dev_ebs_encryption.key_id
#     volume_size = 200
#   }

#   tags = {
#     Name = "qr-ec2-dev-instance"
#   }

#   depends_on = [
#     module.vpc
#   ]
# }

# resource "aws_security_group" "qr_ec2_dev_sg" {
#   name        = "qr-ec2-dev"
#   description = "qr dev security group"
#   vpc_id      = module.vpc.vpc_id

#   tags = {
#     Name = "dev sg"
#   }
# }

# resource "aws_security_group_rule" "qr_ec2_dev_ssh" {
#   type              = "ingress"
#   from_port         = 22
#   to_port           = 22
#   protocol          = "tcp"
#   cidr_blocks       = ["0.0.0.0/0"]
#   security_group_id = aws_security_group.qr_ec2_dev_sg.id
# }

# resource "aws_security_group_rule" "qr_ec2_dev_allow_all" {
#   type              = "egress"
#   to_port           = 0
#   protocol          = "-1"
#   from_port         = 0
#   security_group_id = aws_security_group.qr_ec2_dev_sg.id
#   cidr_blocks       = ["0.0.0.0/0"]
# }

# resource "aws_iam_instance_profile" "qr_ec2_dev_profile" {
#   name = "qr_ec2_dev_profile"
#   role = aws_iam_role.qr_ec2_dev.name
# }

# resource "aws_iam_role" "qr_ec2_dev" {
#   name = "qr_ec2_dev_role"
#   path = "/"

#   assume_role_policy = <<EOF
# {
#     "Version": "2012-10-17",
#     "Statement": [
#         {
#             "Action": "sts:AssumeRole",
#             "Principal": {
#                "Service": "ec2.amazonaws.com"
#             },
#             "Effect": "Allow",
#             "Sid": ""
#         }
#     ]
# }
# EOF
# }

# resource "aws_iam_role_policy_attachment" "qr_ec2_dev_ssm-core-attach" {
#   role       = aws_iam_role.qr_ec2_dev.name
#   policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
# }

# resource "aws_iam_role_policy_attachment" "admin-attach" {
#   role       = aws_iam_role.qr_ec2_dev.name
#   policy_arn = "arn:aws:iam::aws:policy/AdministratorAccess"
# }