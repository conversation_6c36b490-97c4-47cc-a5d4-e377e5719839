# data "aws_eks_cluster" "cluster" {
#   name = module.eks.cluster_name
#   depends_on = [module.eks]
# }

# data "aws_eks_cluster_auth" "cluster" {
#   name = module.eks.cluster_name
#   depends_on = [module.eks]
# }

# data "aws_iam_policy_document" "karpenter" {
#   statement {
#     sid     = "EKSNodeAssumeRole"
#     actions = ["sts:AssumeRole"]

#     principals {
#       type        = "Service"
#       identifiers = ["ec2.amazonaws.com"]
#     }
#   }
# }

# resource "aws_iam_role" "karpenter" {
#   name        = "Karpenter-${var.cluster_name}-${var.stage}"
#   # description = var.karpenter_iam_role_description
#   assume_role_policy    = data.aws_iam_policy_document.karpenter.json
#   force_detach_policies = true
#   # tags = var.additional_tags
# }

# resource "aws_iam_role_policy_attachment" "karpenter" {
#   for_each = { for k, v in toset(compact([
#     "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy",
#     "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly",
#     "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy",
#     "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore",
#   ])) : k => v }

#   policy_arn = each.value
#   role       = aws_iam_role.karpenter.name
# }

# # Create a custom policy document for the Karpenter controller
# data "aws_iam_policy_document" "karpenter_controller_policy" {
#   statement {
#     sid       = "KarpenterPassRole"
#     effect    = "Allow"
#     actions   = ["iam:PassRole"]
#     resources = ["arn:aws:iam::825569692836:role/KarpenterNodeRole-${var.cluster_name}-${var.stage}"]
#   }
# }

# # Create the custom policy
# resource "aws_iam_policy" "karpenter_controller_policy" {
#   name        = "KarpenterControllerPassRole-${var.cluster_name}-${var.stage}"
#   description = "Allow Karpenter to pass role to EC2 instances"
#   policy      = data.aws_iam_policy_document.karpenter_controller_policy.json
# }

# data "aws_ecrpublic_authorization_token" "token" {
#   provider = aws.us-east-1
# }

# module "karpenter" {
#   source                          = "terraform-aws-modules/eks/aws//modules/karpenter"
#   version                         = "~> 20.0"
#   cluster_name                    = module.eks.cluster_name
#   enable_irsa                     = true
#   irsa_oidc_provider_arn          = module.eks.oidc_provider_arn
#   irsa_namespace_service_accounts = ["karpenter:karpenter"]
#   create_instance_profile         = true
#   create_iam_role                 = true
#   create_node_iam_role            = false
#   node_iam_role_arn               = aws_iam_role.karpenter.arn
#   create_access_entry             = true
  
#   tags = {
#     Cluster     = module.eks.cluster_name
#     Environment = var.stage
#     Terraform   = "true"
#   }

#   depends_on = [
#     module.eks
#   ]
# }

# # Attach the custom policy to the Karpenter controller role created by the module
# resource "aws_iam_role_policy_attachment" "karpenter_controller_pass_role" {
#   role       = module.karpenter.iam_role_name
#   policy_arn = aws_iam_policy.karpenter_controller_policy.arn
  
#   depends_on = [
#     module.karpenter
#   ]
# }

# resource "helm_release" "karpenter-crd" {
#   namespace        = "karpenter"
#   create_namespace = true

#   name                = "karpenter-crd"
#   repository          = "oci://public.ecr.aws/karpenter"
#   repository_username = data.aws_ecrpublic_authorization_token.token.user_name
#   repository_password = data.aws_ecrpublic_authorization_token.token.password
#   chart               = "karpenter-crd"
#   version             = "1.3.2"

#   depends_on = [
#     module.eks,
#     module.karpenter
#   ]
# }

# resource "helm_release" "karpenter" {
#   namespace        = "karpenter"
#   create_namespace = true

#   name                = "karpenter"
#   repository          = "oci://public.ecr.aws/karpenter"
#   repository_username = data.aws_ecrpublic_authorization_token.token.user_name
#   repository_password = data.aws_ecrpublic_authorization_token.token.password
#   chart               = "karpenter"
#   version             = "1.3.2"

#   values = [
#     <<-EOT
#     settings:
#       clusterName: ${module.eks.cluster_name}
#       clusterEndpoint: ${module.eks.cluster_endpoint}
#       interruptionQueue: ${module.karpenter.queue_name}
#     serviceAccount:
#       annotations:
#         eks.amazonaws.com/role-arn: ${module.karpenter.iam_role_arn}
#     tolerations:
#       - key: "Karpenter"
#         operator: "Exists"
#     EOT
#   ]

#   depends_on = [
#     helm_release.karpenter-crd
#   ]
# }