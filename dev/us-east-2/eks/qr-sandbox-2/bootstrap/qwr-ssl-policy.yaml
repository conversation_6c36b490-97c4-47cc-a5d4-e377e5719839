apiVersion: kyverno.io/v1
kind: ClusterPolicy
metadata:
  name: sync-qwrnet-ssl
spec:
  rules:
  - name: sync-qwrnet-ssl
    match:
      any:
      - resources:
          kinds:
          - Namespace
    generate:
      apiVersion: v1
      kind: Secret
      name: qwrnet-bundle-tls
      namespace: "{{request.object.metadata.name}}"
      synchronize: true
      data:
        kind: Secret
        metadata:
          name: qwrnet-bundle-tls
        type: kubernetes.io/tls
        data:
          tls.crt: 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
          tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JSUV2Z0lCQURBTkJna3Foa2lHOXcwQkFRRUZBQVNDQktnd2dnU2tBZ0VBQW9JQkFRQzVCenlaa0g2TE9VVUsKeG9seVJUZGpydUdTT09rZHVxbGNmS05KS2t4MFZEbXVoQmkxaVpNVmIyaGQrWkxtcWpiOWNITlM5WXJNN3ZlRAp0c2I4bFZ4dUNaamRKUnc3U3Q0TWE1VHNCYlN4MTRDNUg4WE5KdDBZYmJ4cWxoZG95N0lURlpTTnZNRTNiWTRxCjFnYVdlY3dMU3d3ZldDdy8rUkhMcDRSTUl0WnNxS0FjODJPQnBENkxWdVhVZDlLV3IzTnBhS0NHbERkQTZYSDYKckp0K3ZzejVKSWFDeUg1L3FNZVV1VWhFaHNlZWNWelVUeUVhazJxSlJUYmdoRUpGSm40M3plY2JJZHBaRE9sWgpKVXBJLzdHZjhhd2lYdm9adHlhVnU5b25CenhpV2xhK29OZkxCZGdPdjJmMGlxVldYcXlYR25vSkZQVWRoLzExClptUW9LdjZEQWdNQkFBRUNnZ0VBSTEzMXdHaTFONGwvMy9wcEpWV0RxK011ck5PQ0E3TzlVYmRsUXNQNkR6WlcKUER4MXVLV09Yc2VIeklhN0JLbytqcDBpL2hWL0xZTHovdEkrSU9PdjRqeWxZb2dJYUhEOEZxV3R0cHUwbVZYVwovZ3ZMdEZZY1p1SDY1T2pCbXI5c3oyY05tVFllUEtrTlJyVFlWNFQ2Y3FJcXRBY1hsamJXcHZYalBPWmYxQzduCjJqaCtKbFYrK3JCUi96bDMyYjEwZGZlZ0JzS0VFZjVDVUVrK2lNMVpkdGo2YmxuaCsvMEdDYnF1dlE0UklITk4KNHdSaWNZWGRuREExQkkxODNMejBIaTBxLzBHWnZQd3VWV3U5WTYwY2VtaDJmYzJTTVp0VFZjMjd6cWNZQ0tQeQp6b2lqR1cvQVVnRWh3NUhSb05SVUR0MGhnWTd6Y3A3MHl2cFpJdjlMc1FLQmdRRHBzU2NFYTRaQTBJN2NLTVorCmxJd0QxWDMxSDhwR1VYdDVnd1hDYy8yeThBeWsrclh4L0QwbW5wS21JUGlJek00cTJDUFMrNURoODhZazNhM2MKUFhNcnNGUWZFUVJPSnpZeE92dnc3U2JiMUtMT2s3dEJRdDNTamlReUdBQytsa00rRUhVaUNVazFYYktYRGtQQQpaeVZLeDBvNnFWcmFORUpFVitvVkI1N1VLd0tCZ1FES3NOMU9JTS8xNjFudmtCMStYaDB0cWR6djBCcXFrRy9kClpqQW85emhScmFkNllwRDFUNkxEb0RRU0VrcVVHNHVRZzhhbUxsOFFlemczQzdkOXRldUE1VUFCZEtLbGdMUE8KNlhTNGF2clJHbzg5anpYaEhWekxGaG0zM2o4NlRaSjZucHduMUFldVV4amd2bEFiVjRlWHFBVmZpOXZ0Sk1YNApXRmFGVmg4YkNRS0JnUURwTVZxaTRYejlibUF3Tnd3andhMmFJYzduN01vV3N6YUlRWU5jMEF2b0kvd3FJUGV3Ck9pajNRYU5LM1huRVlNS1dKRXBUUGE4TW1nb3VQWHRCVzV4eWl5aldPMC9WZ1BGbDNOc05Sa2pSY25UbnVLbFIKN1J5WEFEb0VOc2wvWlVoQ1JYL3M3K0U2dkgzNm1FUnBXVVB3UTcvQm03OUtJSDNEM3dwNTQ3bk5Nd0tCZ0NwRApRSkhHMDdYaU1lbDZzanVrSWc5Z2FMbjBPV3N4SExJY1daUmhIL1FmOGxqWmJvSkg3WmMrc3p6RlpabUdPTTZVCk8zcHhGczlYOGZ2QVRLMUFubTlNM2RDdjRpNENMUHZLdjlTb01jNGg3SllZWTFLaURiUW0wb3Y5SWsvazF0TU0KZVdEd20vMWtSRDZ3VUFHemVZbE10bm9hcm11L0RDeGhOd0VzV09ZSkFvR0JBTXlzVmNJM3Jzdk8yV3dFc3pBWApCMzBhd3hjWnFoOWd3VzFHVGc5WmFwaGEyQVJCVEp2YW5jd1NBMkhqRC9XdStOVEtDSVpSZ2xkckhYTEZ2bzEvCmwzQXBXai9lc2R4MmdKcCtwZUFEdE0vaFlMOWpJUjgxYnBFZHhXRk1DRXlVUVA2K1BmVERoZDNSWTdocS9YU1AKcjdZWGNWZzlIRlFzd3NIa0hHQXRiODZnCi0tLS0tRU5EIFBSSVZBVEUgS0VZLS0tLS0=
  - name: sync-qwrnet-ssl-vault
    match:
      any:
      - resources:
          kinds:
          - Namespace
          selector:
            matchLabels:
              kubernetes.io/metadata.name: vault
    generate:
      apiVersion: v1
      kind: Secret
      name: qwrnet-bundle-tls
      namespace: "vault"  # Hardcoding the namespace name here
      synchronize: true
      data:
        kind: Secret
        metadata:
          name: qwrnet-bundle-tls
        type: kubernetes.io/tls
        data:
          tls.crt: 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
          tls.key: 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