apiVersion: v1
kind: Secret
metadata:
  name: qwrnet-tls-secret
  namespace: kube-system
  annotations:
    reflector.v1.k8s.emberstack.com/reflection-allowed: "true"
    reflector.v1.k8s.emberstack.com/reflection-auto-enabled: "true"
    reflector.v1.k8s.emberstack.com/reflection-allowed-namespaces: "argocd,argo,argo-events,b64,vault,cattle-system,kubernetes-dashboard,kargo,nextcloud,pgadmin,wikijs,monitoring,atlantis,harbor,notes,listit,n8n"
    reflector.v1.k8s.emberstack.com/reflection-auto-namespaces: "argocd,argo,argo-events,b64,vault,cattle-system,kubernetes-dashboard,kargo,nextcloud,pgadmin,wikijs,monitoring,atlantis,harbor,notes,listit,n8n"
type: kubernetes.io/tls
data:
  tls.crt: 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
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tDQpNSUlFdlFJQkFEQU5CZ2txaGtpRzl3MEJBUUVGQUFTQ0JLY3dnZ1NqQWdFQUFvSUJBUUNydXFIQVZIZG51MTNyDQpCSnNId0cvdW5ZZkdESm9EU2VZOUxvTnFPMzExU2ZpSlRnbzRzOU9KMTlVR2NTWVJMTlZiK1hibVU4Z2FEVjZMDQo0OHByeEc4MHJCYWFkSWVISUZrZHF1dUF6TEJkZ0pXeG0yMmh6UGVOcmxvN2dvOFhNTHhpYlk1bWVqdUd5WXQ4DQo2bi9UeklnUm5TQU1MYWcySkk0d2sybWlqRE5CL2FvZCt0WlZrek5DTkxDQ2FqZlM2cUhsaGJibzJOaExGeUtoDQo1QWhiVVdNaCtKaE52RWhtdmdJL21DM3BEVDJOQlB2dk5abjlFUkVMMFVwNjFWVzlKNm9RdWtNSFhUYnJkUFA5DQpaU2dYU2xRWkpQVDVQWGdkeG94NGpZVFFtL0RPdktWbDJFL3Nwb3FUYkZmS0FHNXRJNmxDNFB1a0NSamZCTmsrDQpzL3ZoT0dmYkFnTUJBQUVDZ2dFQUVxL2E1UjVyNWVaT0tJSldTbWdtNWZ6UFp2NDlEaW1PKzg1aGI0QnN2UVFoDQpIdlBXaWVnTGwxN2NwL2hORDZaa3l6N2dzTnlpRkMrclA2YTRnclNiODB2Y284OFZabDlmUXlxenVNN05URWR2DQpRcVhxcnFZcXRBaXFlZnBSanREM1Z2Q2NBOHUrU1lNS0FqeFVUNXhjbkNvUWNETFJzSjZXdUhONkZiMlM0RlhIDQo2VE9QVzQ2N3BlYTlnUDBMUjNaK0pjTnczRFVMb01DdTZ2aC9OdllVbTN4dUl2eVIxRG1SU0tnREZTS1ZvOVNaDQoyQ011NEJOUWMwTzRQeTRpaDRWWE9hMWJLeTdoVFlzTGsvL3NyNWt4dzFOUVJWKzlxTysva3c0T3MwMFRMN0FYDQpSaGt5RS94bjN3NVJDNlRRMzNiZWtZTGppZ3EwVlBuOXc1OVVVZnl5UFFLQmdRRGMwSVFuTmQ5dy8xeEFTUFNqDQpiT1RLZzBHUWdhdDh2SzlPdHdTaU50cG9yVENNdVNPOVVnK1dmREFFQVlML1FjOGdYU0RNQnRKMnUvbGthSklzDQpVMVA2SDZueHhCWEl3Qk5qenU3Y3hpK1pmUjJmMVVPeWdicTduV0ZrVmU4OXF4Z0U1SnN1aHJ3amEzSWRpMGFEDQpYWlVwODVFRGVxMm9XQ0JqMHlEK3J6SXFCd0tCZ1FESEY5RUVWdHJUVHJJTXcyMlFwYVAvK1doZG9CaHE2TlpMDQpnTWdxdS9uSTZuTmdxTldjWDJNSmFreTYwTStvalZ3cjZ1end2S1FRb0RYM2N2a3JuWThyT3NIc2V2aTUvS0Z3DQpEVStnV3kyR3FtdXQ3TnJ6KzNJb2M3cnZLV1RNWWlYYm1rS1pNcldEaENlR3AwTjFEcGkrcDR5R283aTQveUxTDQpPbCsyVGlZdWpRS0JnUUROSm8waFFOUVBUN0NOcVRwZ2UxTTFNeTArN0xvK2NhOE9Hc3ViU2dzeEpYYnR4elNGDQpPREJ6UjMzMHV0TFNCSHBEUDBLeFM4Vk51dWJCZXNZVG1IZDA4dVlaVUd4TXp1YzAyNVRnV09Ed1lXV2NrYThMDQpzYXVqazF4Z3M5WktIU043cUVqa3VSa2liemplYWY5aEhSandtUElwb1ZpbUJ2cFF4RjdyUUNvREVRS0JnRVgxDQppMy8vMlRNSll5MXkrcXdxV0YrWlQyQWU0cFdtcmE2b1dTNFRFYW5WSUtmbGkvSS8zbmd2ZnpqaUtTWXNUaWdzDQpqQWZuOGFyWjhUeTdqTzBUbDN4MFVESjkzNnJUS0JOTXloOHFDdXhZTkRCUW0zdkNicEhpWU55cmFmMGJ1L3ZYDQp6L0V2VFhNaVhCVkg3ejlSMnNDYjlaaFdMWkhzTFZzOEllZll2RGY5QW9HQVJtejRlS29OY3FXZGwrOEx1Q1JXDQpQZ3gvNWNGRjVkK2FmU2ZzTlNWK05oSzJZaUhiaGV4TTY0Z1RxaVhna3lSWjczWVBkQ3dMWEpVaFRvN3JnU2hkDQphdHJDRVhjU2ZTMGxYT05oSVZxUTNDdC9qMlBWSXQ2VEdQS0VCcEtYVDNwcnJrek5QMlNqNFB2WlpyMVplNkFnDQpqdC9JSFpPWUdvL1ZyZVN4UlRTZHZRWT0NCi0tLS0tRU5EIFBSSVZBVEUgS0VZLS0tLS0NCg==
