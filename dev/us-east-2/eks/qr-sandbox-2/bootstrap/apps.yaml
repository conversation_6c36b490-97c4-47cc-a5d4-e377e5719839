apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: apps
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io # Cascade deleting app's resources
spec:
  project: default
  source:
    repoURL: **************:qwrnet/eks-manifests.git
    targetRevision: HEAD
    path: _argo-apps
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd
  syncPolicy:
    automated:
      prune: true
