apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-cm
  namespace: argocd
  labels:
    app.kubernetes.io/name: argocd-cm
    app.kubernetes.io/part-of: argocd
data:
  url: https://argocd.qwrobins.net
  dex.config: |
    connectors:
      # GitHub
      - type: github
        id: github
        name: GitHub
        config:
          clientID: b7d67c2a7113c1ac9023
          clientSecret: d9b6fee64f4910d193a0ecfa8ec997c7bacd5523
          orgs:
          - name: qwrnet
            teams:
              - admin
  helm.repositories: |
    - url: https://charts.appscode.com/stable/
      name: appscode
  repositories: |
    - url: **************:qwrnet/eks-manifests.git
      sshPrivateKeySecret:
        name: gitlab-ssh
        key: sshPrivateKey
  accounts.qwrobins: apiKey, login
  kustomize.buildOptions: --enable-helm
