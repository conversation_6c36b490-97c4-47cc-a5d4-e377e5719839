data "aws_ssm_parameter" "eksami" {
  name = format("/aws/service/eks/optimized-ami/%s/amazon-linux-2/recommended/image_id", module.eks.cluster_version)
}

# data "kubernetes_service" "nginx_ingress" {
#   metadata {
#     name      = "ingress-nginx-controller"
#     namespace = "ingress-nginx"
#   }

#   depends_on = [
#     helm_release.nginx
#   ]
# }

data "cloudflare_zone" "qwrnet" {
  name = "qwrobins.net"
}

data "aws_caller_identity" "current" {}

output "account_id" {
  value = data.aws_caller_identity.current.account_id
}

output "caller_arn" {
  value = data.aws_caller_identity.current.arn
}

output "caller_user" {
  value = data.aws_caller_identity.current.user_id
}
