terraform {
  required_providers {
    rancher2 = {
      source = "rancher/rancher2"
    }

    kubectl = {
      source = "gavi<PERSON><PERSON><PERSON>/kubectl"
    }

    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 3.0"
    }
  }
}

provider "cloudflare" {
  api_token = var.cloudflare_api_token
}

provider "aws" {
  # access_key = data.vault_kv_secret_v2.dev_creds.data.lp_dev_aws_access_key_id
  # secret_key = data.vault_kv_secret_v2.dev_creds.data.lp_dev_aws_secret_access_key
  region = "us-east-2"
}

provider "aws" {
  alias = "us-east-1"
  # access_key = data.vault_kv_secret_v2.dev_creds.data.lp_dev_aws_access_key_id
  # secret_key = data.vault_kv_secret_v2.dev_creds.data.lp_dev_aws_secret_access_key
  region = "us-east-1"
}

provider "kubernetes" {
  host                   = module.eks.cluster_endpoint
  cluster_ca_certificate = base64decode(module.eks.cluster_certificate_authority_data)
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "aws"
    args = ["eks", "get-token", "--cluster-name", module.eks.cluster_name, "--region", "us-east-1"]
  }
}

provider "helm" {
  kubernetes {
    host                   = module.eks.cluster_endpoint
    cluster_ca_certificate = base64decode(module.eks.cluster_certificate_authority_data)
    exec {
      api_version = "client.authentication.k8s.io/v1beta1"
      command     = "aws"
      args = ["eks", "get-token", "--cluster-name", module.eks.cluster_name, "--region", "us-east-1"]
    }
  }
}

provider "kubectl" {
  host                   = module.eks.cluster_endpoint
  cluster_ca_certificate = base64decode(module.eks.cluster_certificate_authority_data)
  load_config_file       = false
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    command     = "aws"
    args = ["eks", "get-token", "--cluster-name", module.eks.cluster_name, "--region", "us-east-1"]
  }
}
