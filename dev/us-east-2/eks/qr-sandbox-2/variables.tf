variable "cluster_name" {
  type    = string
  default = "qr-sandbox"
}

variable "namespace" {
  default = "lp-swat"
}

variable "stage" {
  default = "dev"
}

variable "vpc_cidr" {
  type    = string
  default = "12.0.0.0/16"
}

variable "azs" {
  default = ["us-east-2a", "us-east-2b", "us-east-2c"]
}

variable "public_subnet_cidrs" {
  default = ["12.0.1.0/24", "12.0.2.0/24", "12.0.3.0/24"]
}

variable "private_subnets" {
  default = []
}

variable "private_subnet_cidrs" {
  default = ["12.0.4.0/24", "12.0.5.0/24", "12.0.6.0/24"]
}

variable "oidc_thumbprint_list" {
  default = []
}

variable "iam_path" {
  default = "/"
}

variable "instance_types" {
  type    = list(string)
  default = ["t3.xlarge"]
}

variable "aws_sbox_access_key" {
  default = ""
}

variable "aws_sbox_secret_key" {
  default = ""
}

variable "docker_username" {
  default = ""
}

variable "docker_password" {
  default = ""
}

variable "lp_gitops_rsa" {
  default = ""
}

variable "http_port" {
  default = 31816
}

variable "https_port" {
  default = 31046
}

variable "cloudflare_api_token" {
  type    = string
  default = "eXWK3eTweFgyJjWxWCp048lXtHrc2hsoW6HOD8rv"
}

variable "efs_namespaces" {
  type = list(string)
  default = ["nextcloud", "utils", "postgres"]
}

variable "utils_namespaces" {
  type    = list(string)
  default = ["utils", "monitoring"]
}
