# This file manages dependencies between resources to avoid circular references

# Create a local variable for the cluster name to ensure consistency
locals {
  cluster_name = "${var.cluster_name}-${var.stage}"
}

# Create a null resource that depends on the EKS cluster
# This helps establish a clear dependency chain
resource "null_resource" "eks_cluster_dependency" {
  depends_on = [module.eks]

  # This will only run when the EKS cluster is fully created
  provisioner "local-exec" {
    command = "echo 'EKS cluster ${local.cluster_name} is ready'"
  }
}
