locals {
  dns_records = {
    "rancher"        = "rancher"
    "acd"            = "argocd"
    "vault"          = "vault"
    "traefik"        = "traefik"
    "k8s_dashboard"  = "eks"
    "ceph_dashboard" = "rook"
    "idm"            = "idm"
    "awf"            = "awf"
    "pgadmin"        = "pgadmin"
    "wiki"           = "wiki"
    "blog"           = "blog"
    "grafana"        = "grafana"
    "atlantis"       = "atlantis"
    "kargo"          = "kargo"
    "notes"          = "notes"
    "n8n"            = "n8n"
  }
}

resource "cloudflare_record" "k8s_records" {
  for_each = local.dns_records
  
  zone_id = data.cloudflare_zone.qwrnet.id
  name    = each.value
  type    = "CNAME"
  value   = aws_lb.k8s_lb.dns_name
  proxied = false
}

# output "ld_account_id" {
#   value = data.cloudflare_zone.ldevops.account_id
# }

# output "ld_zone_id" {
#   value = data.cloudflare_zone.ldevops.id
# }
