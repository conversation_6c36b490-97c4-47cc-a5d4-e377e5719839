{"addons": [{"addonName": "solo-io_istio-distro", "type": "service-mesh", "addonVersions": [{"addonVersion": "v1.24.2-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": false}], "publisher": "Solo.io", "owner": "aws-marketplace", "marketplaceInformation": {"productId": "bc18df1e-655f-4469-a969-600c0ec256db", "productUrl": "https://aws.amazon.com/marketplace/pp/prodview-kvr2wqekzmuhi"}}, {"addonName": "aws-guardduty-agent", "type": "security", "addonVersions": [{"addonVersion": "v1.8.1-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.8.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.7.1-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.7.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.6.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}], "publisher": "eks", "owner": "aws"}, {"addonName": "aws-network-flow-monitoring-agent", "type": "observability", "addonVersions": [{"addonVersion": "v1.0.1-eksbuild.2", "architecture": ["amd64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.0.0-eksbuild.4", "architecture": ["amd64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}], "publisher": "eks", "owner": "aws"}, {"addonName": "solarwinds_swo-k8s-collector-addon", "type": "observability", "addonVersions": [{"addonVersion": "v4.3.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": true, "requiresIamPermissions": false}], "publisher": "SolarWinds", "owner": "aws-marketplace", "marketplaceInformation": {"productId": "prod-tqdc264ve2utc", "productUrl": "https://aws.amazon.com/marketplace/pp/prodview-tddekyrun3vxi"}}, {"addonName": "kube-proxy", "type": "networking", "addonVersions": [{"addonVersion": "v1.31.3-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.31.2-eksbuild.3", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.31.2-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.31.1-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.31.0-eksbuild.5", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.31.0-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.30.7-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.30.6-eksbuild.3", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.30.6-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.30.5-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.30.3-eksbuild.9", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.30.3-eksbuild.5", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.30.3-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.29.11-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.29.10-eksbuild.3", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.29.10-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.29.9-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.29.7-eksbuild.9", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.29.7-eksbuild.5", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.29.7-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.28.8-eksbuild.5", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}], "publisher": "eks", "owner": "aws"}, {"addonName": "dynatrace_dynatrace-operator", "type": "monitoring", "addonVersions": [{"addonVersion": "v1.4.0-eksbuild.1", "architecture": ["arm64", "amd64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.3.2-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}], "publisher": "dynatrace", "owner": "aws-marketplace", "marketplaceInformation": {"productId": "6f9d8708-95c6-46cf-94db-6717b8dbda17", "productUrl": "https://aws.amazon.com/marketplace/pp/prodview-brb73nceicv7u"}}, {"addonName": "akuity_agent", "type": "gitops", "addonVersions": [{"addonVersion": "v0.8.6-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": true, "requiresIamPermissions": false}], "publisher": "aku<PERSON>", "owner": "aws-marketplace", "marketplaceInformation": {"productId": "e20fce9c-0a34-4878-aa32-028954620986", "productUrl": "https://aws.amazon.com/marketplace/pp/prodview-zihrsklxqjuu6"}}, {"addonName": "metrics-server", "type": "observability", "addonVersions": [{"addonVersion": "v0.7.2-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "fargate", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": false}], "publisher": "eks", "owner": "community"}, {"addonName": "netapp_trident-operator", "type": "storage", "addonVersions": [{"addonVersion": "v24.10.0-eksbuild.1", "architecture": ["arm64", "amd64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": true, "requiresIamPermissions": false}], "publisher": "NetApp Inc.", "owner": "aws-marketplace", "marketplaceInformation": {"productId": "3dddaaf1-373c-40ce-91ce-26cec94088eb", "productUrl": "https://aws.amazon.com/marketplace/pp/prodview-mptzckr6mggia"}}, {"addonName": "aws-mountpoint-s3-csi-driver", "type": "storage", "addonVersions": [{"addonVersion": "v1.11.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.10.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.9.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.8.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.8.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.7.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.6.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}], "publisher": "s3", "owner": "aws"}, {"addonName": "coredns", "type": "networking", "addonVersions": [{"addonVersion": "v1.11.4-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "fargate", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.11.4-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "fargate", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.11.3-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "fargate", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.11.3-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "fargate", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.11.1-eksbuild.13", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "fargate", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.11.1-eksbuild.11", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "fargate", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.10.1-eksbuild.18", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "fargate", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.10.1-eksbuild.17", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "fargate", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.10.1-eksbuild.15", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "fargate", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.10.1-eksbuild.13", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "fargate", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}], "publisher": "eks", "owner": "aws"}, {"addonName": "adot", "type": "observability", "addonVersions": [{"addonVersion": "v0.109.0-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "fargate", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v0.109.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "fargate", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}], "publisher": "eks", "owner": "aws"}, {"addonName": "amazon-cloudwatch-observability", "type": "observability", "addonVersions": [{"addonVersion": "v3.1.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v3.0.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.6.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.5.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.4.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.3.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.3.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.2.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.2.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.1.3-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.1.2-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.1.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.1.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.0.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.0.0-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}], "publisher": "eks", "owner": "aws"}, {"addonName": "snapshot-controller", "type": "storage", "addonVersions": [{"addonVersion": "v8.1.0-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid", "auto", "fargate"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v8.1.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v8.0.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v7.0.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v6.3.2-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v6.2.2-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}], "publisher": "eks", "owner": "aws"}, {"addonName": "eks-pod-identity-agent", "type": "security", "addonVersions": [{"addonVersion": "v1.3.4-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "hybrid"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.3.2-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.3.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.2.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.1.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.0.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}], "publisher": "eks", "owner": "aws"}, {"addonName": "solo-io_gloo-gateway", "type": "ingress-controller", "addonVersions": [{"addonVersion": "v1.18.5-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": true, "requiresIamPermissions": false}, {"addonVersion": "v1.18.2-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}], "publisher": "Solo.io", "owner": "aws-marketplace", "marketplaceInformation": {"productId": "prod-yewbssna2fk6y", "productUrl": "https://aws.amazon.com/marketplace/pp/prodview-btlli6o5q2vbm"}}, {"addonName": "kubecost_kubecost", "type": "cost-management", "addonVersions": [{"addonVersion": "v2.4.3-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": false}], "publisher": "kubecost", "owner": "aws-marketplace", "marketplaceInformation": {"productId": "753cea16-f450-4cfa-93eb-f55dcde11e91", "productUrl": "https://aws.amazon.com/marketplace/pp/prodview-asiz4x22pm2n2"}}, {"addonName": "uptycs_uptycs-runtime-sensor", "type": "security", "addonVersions": [{"addonVersion": "v1.0.1-eksbuild.1", "architecture": ["arm64", "amd64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": true, "requiresIamPermissions": false}], "publisher": "Uptycs", "owner": "aws-marketplace", "marketplaceInformation": {"productId": "prod-52md7llv3h4t6", "productUrl": "https://aws.amazon.com/marketplace/pp/prodview-sikdnmdb7llly"}}, {"addonName": "tetrate-io_istio-distro", "type": "policy-management", "addonVersions": [{"addonVersion": "v1.24.10000-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": true, "requiresIamPermissions": false}], "publisher": "tetrate-io", "owner": "aws-marketplace", "marketplaceInformation": {"productId": "976bbc27-7f73-4fe7-afa5-bebbb369316d", "productUrl": "https://aws.amazon.com/marketplace/pp/prodview-rm6w3vwyibt46"}}, {"addonName": "aws-efs-csi-driver", "type": "storage", "addonVersions": [{"addonVersion": "v2.1.4-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.1.3-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.1.2-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.1.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.1.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.0.9-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.0.8-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.0.7-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.0.6-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.0.6-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.0.5-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.0.4-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.0.3-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.0.2-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.0.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v2.0.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2", "auto"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.7.7-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.7.6-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.7.6-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.7.5-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.7.5-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.7.4-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.7.3-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.7.2-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.7.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.7.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.5.9-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.5.8-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}], "publisher": "eks", "owner": "aws"}, {"addonName": "aws-ebs-csi-driver", "type": "storage", "addonVersions": [{"addonVersion": "v1.39.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.38.1-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.38.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.37.0-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.37.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.36.0-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.36.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.35.0-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.35.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.34.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.33.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.32.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.31.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.30.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.29.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}], "publisher": "eks", "owner": "aws"}, {"addonName": "eks-node-monitoring-agent", "type": "observability", "addonVersions": [{"addonVersion": "v1.0.1-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.0.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}], "publisher": "eks", "owner": "aws"}, {"addonName": "upwind-security_upwind-operator", "type": "security", "addonVersions": [{"addonVersion": "v0.5.25-eksbuild.1", "architecture": ["arm64", "amd64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": false}], "publisher": "Upwind Security", "owner": "aws-marketplace", "marketplaceInformation": {"productId": "prod-iuipntteq5qxq", "productUrl": "https://aws.amazon.com/marketplace/pp/prodview-na5n7esfru5ig"}}, {"addonName": "new-relic_kubernetes-operator", "type": "observability", "addonVersions": [{"addonVersion": "v0.1.10-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": false}], "publisher": "New Relic", "owner": "aws-marketplace", "marketplaceInformation": {"productId": "c43a5e64-6708-42f2-85f7-29d2e350767e", "productUrl": "https://aws.amazon.com/marketplace/pp/prodview-gcywa6keq2ajy"}}, {"addonName": "amazon-sagemaker-hyperpod-taskgovernance", "type": "policy-management", "addonVersions": [{"addonVersion": "v1.0.0-eksbuild.4", "architecture": ["amd64", "arm64"], "computeTypes": ["hyperpod"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": false}, {"addonVersion": "v1.0.0-eksbuild.3", "architecture": ["amd64", "arm64"], "computeTypes": ["hyperpod"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": false}], "publisher": "sagemaker", "owner": "aws"}, {"addonName": "uptycs_uptycs-collector", "type": "security", "addonVersions": [{"addonVersion": "v1.1.2-eksbuild.1", "architecture": ["amd64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": true, "requiresIamPermissions": false}], "publisher": "Uptycs", "owner": "aws-marketplace", "marketplaceInformation": {"productId": "prod-gpjrkqzplvnsw", "productUrl": "https://aws.amazon.com/marketplace/pp/prodview-n2ya7cmyxezsi"}}, {"addonName": "cribl_cribledge", "type": "observability", "addonVersions": [{"addonVersion": "v4.9.3-eksbuild.1", "architecture": ["arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": true, "requiresIamPermissions": false}], "publisher": "Cribl", "owner": "aws-marketplace", "marketplaceInformation": {"productId": "956e78a4-f121-4029-9908-9ad395a4cfb6", "productUrl": "https://aws.amazon.com/marketplace/pp/prodview-6bmpo32ebx6gg"}}, {"addonName": "vpc-cni", "type": "networking", "addonVersions": [{"addonVersion": "v1.19.2-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.19.0-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.18.6-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.18.5-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.18.4-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.18.3-eksbuild.3", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.18.3-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.17.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}, {"addonVersion": "v1.16.4-eksbuild.2", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": false}], "requiresConfiguration": false, "requiresIamPermissions": true}], "publisher": "eks", "owner": "aws"}, {"addonName": "snyk_runtime-sensor", "type": "security", "addonVersions": [{"addonVersion": "v1.76.1-eksbuild.1", "architecture": ["amd64", "arm64"], "computeTypes": ["ec2"], "compatibilities": [{"clusterVersion": "1.31", "platformVersions": ["*"], "defaultVersion": true}], "requiresConfiguration": true, "requiresIamPermissions": false}], "publisher": "Snyk", "owner": "aws-marketplace", "marketplaceInformation": {"productId": "prod-ddxlqvmqhdbd4", "productUrl": "https://aws.amazon.com/marketplace/pp/prodview-uvof3afhvlvea"}}]}