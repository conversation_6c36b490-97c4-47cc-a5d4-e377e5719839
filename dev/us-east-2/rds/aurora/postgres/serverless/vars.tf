locals {
  name   = "qr-postgresqlv2"
  region = "us-east-2"

  tags = {
    Example    = local.name
  }
}

variable "vpc_id" {
  type = string
  default = "vpc-0069116528896b989"
}

variable "private_subnets" {
  type = list
  default = ["12.99.3.0/24", "12.99.4.0/24", "12.99.5.0/24"]
}

variable "db_subnets" {
  type = list
  default = ["subnet-04c39b9e72f5888b8", "subnet-077dfced1f54b9bc7", "subnet-0389e4fed780ca5ae"]
}