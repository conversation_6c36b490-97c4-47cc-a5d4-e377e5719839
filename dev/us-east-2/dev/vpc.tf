module "vpc" {
  source = "terraform-aws-modules/vpc/aws"

  name = "qr-ec2-dev-vpc"

  cidr = var.vpc_cidr

  azs                     = var.azs
  private_subnets         = var.private_subnet_cidrs
  public_subnets          = var.public_subnet_cidrs
  map_public_ip_on_launch = true

  enable_ipv6 = false

  enable_nat_gateway                     = true
  single_nat_gateway                     = true
  enable_dns_hostnames                   = true
  enable_dns_support                     = true
  create_database_subnet_group           = true
  create_database_subnet_route_table     = true
  create_database_internet_gateway_route = false

  public_subnet_tags = {
    Name                                    = "qr-ec2-dev-public"
  }

  private_subnet_tags = {
    Name                                    = "qr-ec2-dev-private"
  }

  tags = {
    Owner       = "QR"
    Environment = "dev-sandbox"
  }

  vpc_tags = {
    Name = "qr-ec2-dev-vpc"
  }
}
