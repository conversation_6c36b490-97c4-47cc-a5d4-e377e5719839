locals {
  dev-userdata = <<USERDATA
MIME-Version: 1.0
Content-Type: multipart/mixed; boundary="==UDBOUNDARY=="

--==UDBOUNDARY==
Content-Type: text/x-shellscript; charset="us-ascii"

#!/bin/bash -xe
echo "Running custom user data script" > /tmp/udata.txt
apt update -y
hostnamectl set-hostname ec2-dev-use2.localdomain

# Create qwrobins user with sudo permissions
useradd -m -s /bin/bash qwrobins
echo "created user qwrobins" >> /tmp/udata.txt
# Add qwrobins to sudo group
usermod -aG sudo qwrobins
echo "added qwrobins to sudo group" >> /tmp/udata.txt
# Allow qwrobins to use sudo without password (optional - remove if you want password prompt)
echo "qwrobins ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers.d/qwrobins
chmod 440 /etc/sudoers.d/qwrobins
echo "configured sudo access for qwrobins" >> /tmp/udata.txt

# Set up SSH key access for qwrobins user
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
# Copy authorized keys from ubuntu user (or from cloud-init metadata)
if [ -f /home/<USER>/.ssh/authorized_keys ]; then
    cp /home/<USER>/.ssh/authorized_keys /home/<USER>/.ssh/authorized_keys
    echo "copied SSH keys from ubuntu user" >> /tmp/udata.txt
else
    # Fallback: get keys from instance metadata
    curl -s http://***************/latest/meta-data/public-keys/0/openssh-key > /home/<USER>/.ssh/authorized_keys
    echo "retrieved SSH keys from metadata" >> /tmp/udata.txt
fi
chmod 600 /home/<USER>/.ssh/authorized_keys
chown -R qwrobins:qwrobins /home/<USER>/.ssh
echo "configured SSH access for qwrobins" >> /tmp/udata.txt

echo "installing dependencies" >> /tmp/udata.txt
apt update && apt install -y build-essential
echo "installed build-essential" >> /tmp/udata.txt
echo "installing additional dependencies" >> /tmp/udata.txt
apt install -y procps file git wget curl gnupg gnupg2 rng-tools gnupg-agent gpg zsh apt-transport-https software-properties-common ca-certificates python3-pip postgresql-client libpq-dev openssh-server g++ libqt5webkit5-dev gstreamer1.0-plugins-base gstreamer1.0-tools gstreamer1.0-x make libssl-dev zlib1g-dev libbz2-dev libreadline-dev libsqlite3-dev llvm libncurses5-dev libncursesw5-dev xz-utils tk-dev libffi-dev liblzma-dev ansible jq btop htop unzip neofetch eza
echo "installed dependencies" >> /tmp/udata.txt

echo "installing docker" >> /tmp/udata.txt
echo "adding docker official gpg key" >> /tmp/udata.txt
apt-get update
install -m 0755 -d /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
chmod a+r /etc/apt/keyrings/docker.asc

echo "adding the repository to Apt sources:" >> /tmp/udata.txt
echo "deb [arch=amd64 signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu noble stable" > /etc/apt/sources.list.d/docker.list
echo "installing docker packages" >> /tmp/udata.txt
apt-get update && apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
echo "installed docker" >> /tmp/udata.txt
echo "adding qwrobins to docker group" >> /tmp/udata.txt
usermod -aG docker qwrobins
echo "docker installed" >> /tmp/udata.txt

echo "installing aws cli" >> /tmp/udata.txt
if [[ ! -f /usr/local/bin/aws ]]; then
  curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
  unzip awscliv2.zip
  ./aws/install
fi

echo "installing golang 1.24.4" >> /tmp/udata.txt
wget https://go.dev/dl/go1.24.4.linux-amd64.tar.gz
rm -rf /usr/local/go && tar -C /usr/local -xzf go1.24.4.linux-amd64.tar.gz
echo "installed golang" >> /tmp/udata.txt

echo "installing tailscale" >> /tmp/udata.txt
curl -fsSL https://pkgs.tailscale.com/stable/ubuntu/noble.noarmor.gpg > /usr/share/keyrings/tailscale-archive-keyring.gpg
curl -fsSL https://pkgs.tailscale.com/stable/ubuntu/noble.tailscale-keyring.list > /etc/apt/sources.list.d/tailscale.list
apt-get update && apt-get install -y tailscale
tailscale up --authkey tskey-auth-kFswebSU9j11CNTRL-ZK8yGwcEqNWXUTpyur7tNW6GQWApxK3i --accept-routes
echo "installed tailscale" >> /tmp/udata.txt

echo "installation completed" >> /tmp/udata.txt

date >> /tmp/udata.txt

--==UDBOUNDARY==--
USERDATA
}