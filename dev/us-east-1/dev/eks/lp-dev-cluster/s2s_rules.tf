# resource "aws_security_group_rule" "vpn_psg_rule" {
#   type              = "ingress"
#   from_port         = 0
#   to_port           = 0
#   protocol          = "-1"
#   cidr_blocks       = ["192.168.0.0/16"]
#   security_group_id = module.eks.cluster_primary_security_group_id
# }

# resource "aws_security_group_rule" "vpn_sg_rule" {
#   type              = "ingress"
#   from_port         = 0
#   to_port           = 0
#   protocol          = "-1"
#   cidr_blocks       = ["192.168.0.0/16"]
#   security_group_id = module.eks.cluster_security_group_id
# }

# resource "aws_security_group_rule" "vpn_wsg_rule" {
#   type              = "ingress"
#   from_port         = 0
#   to_port           = 0
#   protocol          = "-1"
#   cidr_blocks       = ["192.168.0.0/16"]
#   security_group_id = module.eks.worker_security_group_id
# }