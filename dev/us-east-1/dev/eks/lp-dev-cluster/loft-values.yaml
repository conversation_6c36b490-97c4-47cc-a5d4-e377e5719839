# If an admin user should be created during deployment
admin:
  create: true
  username: admin
  password: "admin"

# Loft service options
service:
  type: ClusterIP
  # labels: {}            # {key: value} map of labels for the service
  # annotations: {}       # {key: value} map of annotations for the service

# Ingress options to use loft behind an ingress
ingress:
  enabled: true
  name: loft-ingress
  # labels: {}            # {key: value} map of labels for the ingress
  # annotations: {}       # {key: value} map of annotations for the ingress
  host: loft.luxurydevops.com
  ingressClass: nginx
  path: /
  tls:
    enabled: false

# TLS configuration with a custom cert and key
# Make sure the secret exists prior to deploying loft,
# otherwise the loft pod will not be able to start
tls:
  enabled: false
  secret: loft-tls
  crtKey: tls.crt
  keyKey: tls.key

# Additional annotations for the loft deployment
# annotations: {}

# Additional labels for the loft deployment
# labels: {}

# Additional annotations for the loft pod
# podAnnotations: {}

# Additional common annotations for all resources
# commonAnnotations: {}

# Annotations for the loft-config secret
# secretAnnotations: {}

# Additional labels for the loft pod
# podLabels: {}

# Default values for loft deployment.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# Default value is: loftsh/loft:{{ .Chart.Version }}
# image: loftsh/loft

# Additional enviroment variables in the form of
# VAR_NAME: VAR_VALUE
env: {}

# Additional environment variables in the form of
# VAR_NAME: 
#   secretKeyRef:
#     name: mysecret
#     key: username
envValueFrom: {}

# Replica amount of the loft deployment
replicaCount: 3

# Resources of the loft deployment
resources:
  requests:
    memory: 128Mi
    cpu: 50m
  limits:
    memory: 2Gi
    cpu: "2"

# Additional volumes that should be mounted to the loft deployment
volumes: []

# Additional volume mounts that should be mounted into the loft container
volumeMounts: []

# If the readinessProbe should be enabled
readinessProbe:
  enabled: true

# If the livenessProbe should be enabled
livenessProbe:
  enabled: true

# If an extension api service should be registered for 
# the loft apis in kubernetes
apiService:
  enabled: false

# Additional loft service account options
serviceAccount:
  name: loft
  create: true
  clusterRole: cluster-admin
  annotations: {}

nameOverride: ""
fullnameOverride: ""

# If a cert issuer should be created for loft
certIssuer:
  create: false
  email: "" # This is a required field if create == true
  name: lets-encrypt-http-issuer
  secretName: loft-letsencrypt-credentials
  server: https://acme-v02.api.letsencrypt.org/directory
  httpResolver:
    enabled: true
    ingressClass: nginx
  resolvers: []

# Additional options for audit logging
audit:
  enableSideCar: false
  image: library/alpine:3.13.1
  persistence: 
    enabled: false
    size: 10Gi
    # Optional storage class
    #storageClassName: my-storage-class
    # Optional custom accessModes
    #accessModes: ["ReadWriteOnce"]

# Prometheus Service Monitor Configuration
serviceMonitor:
  enabled: false
  jobLabel: loft
  targetPort: 8080
  path: /metrics
  interval: 60s
  scrapeTimeout: 30s
  labels: {}

# Loft config to use initially
config:
  audit:
    enabled: true

# Enables running loft agent with a security context that:
# - disables privileged escalation
# - drops all capabilities
# - runs as non-root
securityContext:
  enabled: true