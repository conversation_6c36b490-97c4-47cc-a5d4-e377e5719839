# output "vpc_id" {
#   description = "The ID of the VPC"
#   value       = module.vpc.vpc_id
# }

# # CIDR blocks
# output "vpc_cidr_block" {
#   description = "The CIDR block of the VPC"
#   value       = module.vpc.vpc_cidr_block
# }

# Subnets
output "private_subnets" {
  description = "List of IDs of private subnets"
  value       = module.vpc.private_subnets
}

# output "public_subnets" {
#   description = "List of IDs of public subnets"
#   value       = module.vpc.public_subnets
# }

# # NAT gateways
# output "nat_public_ips" {
#   description = "List of public Elastic IPs created for AWS NAT Gateway"
#   value       = module.vpc.nat_public_ips
# }

# # AZs
# output "azs" {
#   description = "A list of availability zones spefified as argument to this module"
#   value       = module.vpc.azs
# }

# output "name" {
#   value = module.eks
# }

output "key_id" {
  value = aws_kms_key.eks_secrets.key_id
}

output "primary_sg" {
  value = module.eks.cluster_primary_security_group_id
}

output "cluster_sg" {
  value = module.eks.cluster_security_group_id
}

output "worker_sg" {
  value = module.eks.worker_security_group_id
}

output "certificate_arn" {
  value = aws_acm_certificate.ldevops.arn
}