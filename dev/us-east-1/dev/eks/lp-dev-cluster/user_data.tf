locals {
  eks-node-private-userdata = <<USERDATA
MIME-Version: 1.0
Content-Type: multipart/mixed; boundary="==UDBOUNDARY=="

--==UDBOUNDARY==
Content-Type: text/x-shellscript; charset="us-ascii"

#!/bin/bash -xe
echo "Running custom user data script" > /tmp/udata.txt
yum install -y amazon-ssm-agent
echo "ssm agent installed successfully" >> /tmp/udata.txt
systemctl enable amazon-ssm-agent && systemctl start amazon-ssm-agent

date >> /tmp/udata.txt

--==UDBOUNDARY==--
USERDATA
}

# sudo /etc/eks/bootstrap.sh --apiserver-endpoint '${module.eks.cluster_endpoint}' --b64-cluster-ca '${module.eks.cluster_certificate_authority_data}' '${module.eks.cluster_id}'