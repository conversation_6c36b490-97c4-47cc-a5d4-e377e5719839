# resource "kubectl_manifest" "lp_admin_sa" {
#   yaml_body = <<-YAML
#   apiVersion: v1
#   kind: ServiceAccount
#   metadata:
#     name: lp-admin
#     namespace: kube-system
#     annotations:
#       eks.amazonaws.com/role-arn: arn:aws:iam::************:role/lp-admin-k8s-role
#   YAML

#   depends_on = [
#     module.eks
#   ]
# }

# resource "kubectl_manifest" "lp_admin_role_binding" {
#   yaml_body = <<-YAML
#   apiVersion: rbac.authorization.k8s.io/v1
#   kind: ClusterRoleBinding
#   metadata:
#     name: lp-admin
#   roleRef:
#     apiGroup: rbac.authorization.k8s.io
#     kind: ClusterRole
#     name: cluster-admin
#   subjects:
#   - kind: ServiceAccount
#     name: lp-admin
#     namespace: kube-system
#   YAML

#   depends_on = [
#     module.eks
#   ]
# }

# resource "kubectl_manifest" "argocd_bootstrap_job" {
#   yaml_body = <<-YAML
#   apiVersion: batch/v1
#   kind: Job
#   metadata:
#     name: argocd-bootstrap
#   spec:
#     template:
#       spec:
#         serviceAccountName: lp-admin
#         containers:
#         - name: argocd-bootstrap
#           image: ************.dkr.ecr.us-west-2.amazonaws.com/argocd-bootstrap-sbox:v1.2.1
#           imagePullPolicy: Always
#           command: ["./bootstrap.sh"]
#         restartPolicy: Never
#     backoffLimit: 4
#   YAML

#   depends_on = [
#     module.eks
#   ]
# }

# resource "kubernetes_manifest" "job_kube_system_argocd_bootstrap_job" {
#   manifest = {
#     "apiVersion" = "batch/v1"
#     "kind"       = "Job"
#     "metadata" = {
#       "name"      = "argocd-bootstrap-job"
#       "namespace" = "kube-system"
#     }
#     "spec" = {
#       "backoffLimit" = 4
#       "template" = {
#         "spec" = {
#           "containers" = [
#             {
#               "command" = [
#                 "./bootstrap.sh",
#               ]
#               "image"           = "************.dkr.ecr.us-west-2.amazonaws.com/argocd-bootstrap-sbox:v1"
#               "imagePullPolicy" = "Always"
#               "name"            = "argocd-bootstrap-job"
#             },
#           ]
#           "restartPolicy"      = "Never"
#           "serviceAccountName" = "lp-admin"
#         }
#       }
#     }
#   }

#   depends_on = [
#     kubectl_manifest.lp_admin
#   ]
# }
