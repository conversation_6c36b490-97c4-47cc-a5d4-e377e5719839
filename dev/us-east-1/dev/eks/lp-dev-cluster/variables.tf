variable "cluster_name" {
  type    = string
  default = "lp"
}

variable "namespace" {
  default = "lp-dev"
}

variable "stage" {
  default = "dev"
}

variable "vpc_cidr" {
  type    = string
  default = "12.0.0.0/16"
}

variable "azs" {
  default = ["us-east-1a", "us-east-1b", "us-east-1c"]
}

variable "public_subnet_cidrs" {
  default = ["12.0.1.0/24", "12.0.2.0/24", "12.0.3.0/24"]
}

variable "private_subnets" {
  default = []
}

variable "private_subnet_cidrs" {
  default = ["12.0.4.0/24", "12.0.5.0/24", "12.0.6.0/24"]
}

variable "oidc_thumbprint_list" {
  default = []
}

variable "iam_path" {
  default = "/"
}

variable "instance_types" {
  type    = list(string)
  default = ["t3.xlarge"]
}

variable "aws_sbox_access_key" {
  default = ""
}

variable "aws_sbox_secret_key" {
  default = ""
}

variable "docker_username" {
  default = ""
}

variable "docker_password" {
  default = ""
}

variable "lp_gitops_rsa" {
  default = ""
}

variable "http_port" {
  default = 31816
}

variable "https_port" {
  default = 31046
}

variable "cloudflare_api_token" {
  type    = string
  default = "5gprHAgteo0Gr5MT-OvTO6AdkwAyyDXLE092x2Wi"
}
