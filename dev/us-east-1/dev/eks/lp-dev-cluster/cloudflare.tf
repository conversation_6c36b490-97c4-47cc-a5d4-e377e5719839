# # resource "cloudflare_record" "rancher" {
# #   zone_id = data.cloudflare_zone.ldevops.id
# #   name    = "rancher"
# #   type    = "CNAME"
# #   value   = aws_lb.k8s_lb.dns_name
# #   proxied = true
# # }

# # resource "cloudflare_record" "nginx" {
# #   zone_id = data.cloudflare_zone.ldevops.id
# #   name    = "nginx"
# #   type    = "CNAME"
# #   value   = aws_lb.k8s_lb.dns_name
# #   proxied = false
# # }

# output "ld_account_id" {
#   value = data.cloudflare_zone.ldevops.account_id
# }

# output "ld_zone_id" {
#   value = data.cloudflare_zone.ldevops.id
# }
