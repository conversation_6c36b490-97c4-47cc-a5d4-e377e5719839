data "aws_ssm_parameter" "eksami" {
  name = format("/aws/service/eks/optimized-ami/%s/amazon-linux-2/recommended/image_id", module.eks.cluster_version)
}

data "aws_eks_cluster" "cluster" {
  name = module.eks.cluster_id
}

data "aws_eks_cluster_auth" "cluster" {
  name = module.eks.cluster_id
}

data "aws_route53_zone" "lcoders" {
  name         = "luxurycoders.com."
  private_zone = false
}

data "aws_route53_zone" "ldevops" {
  name         = "luxurydevops.com"
  private_zone = false
}

# data "kubernetes_service" "nginx_ingress" {
#   metadata {
#     name      = "ingress-nginx-controller"
#     namespace = "ingress-nginx"
#   }

#   depends_on = [
#     helm_release.nginx
#   ]
# }

# data "cloudflare_zone" "ldevops" {
#   name = "luxurydevops.com"
# }

# data "vault_kv_secret_v2" "dev_creds" {
#   mount = "secret"
#   name = "aws-keys"
# }

data "aws_caller_identity" "current" {}

output "account_id" {
  value = data.aws_caller_identity.current.account_id
}

output "caller_arn" {
  value = data.aws_caller_identity.current.arn
}

output "caller_user" {
  value = data.aws_caller_identity.current.user_id
}
