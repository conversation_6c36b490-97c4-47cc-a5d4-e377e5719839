# resource "aws_route53_record" "rancher" {
#   zone_id = data.aws_route53_zone.lcoders.id
#   name    = "qr-dev-k8s.${data.aws_route53_zone.lcoders.name}"
#   type    = "A"
#   alias {
#     name    = aws_lb.k8s_lb.dns_name
#     zone_id = aws_lb.k8s_lb.zone_id
#     # Z18D5FSROUN65G
#     evaluate_target_health = false
#   }
# }

# resource "aws_route53_record" "traefik" {
#   zone_id = data.aws_route53_zone.lcoders.id
#   name    = "traefik.${data.aws_route53_zone.lcoders.name}"
#   type    = "A"
#   alias {
#     name                   = aws_lb.k8s_lb.dns_name
#     zone_id                = aws_lb.k8s_lb.zone_id
#     evaluate_target_health = false
#   }
# }

# resource "aws_route53_record" "react_demo" {
#   zone_id = data.aws_route53_zone.lcoders.id
#   name    = "react-demo.${data.aws_route53_zone.lcoders.name}"
#   type    = "A"
#   alias {
#     name                   = aws_lb.k8s_lb.dns_name
#     zone_id                = aws_lb.k8s_lb.zone_id
#     evaluate_target_health = false
#   }
# }
