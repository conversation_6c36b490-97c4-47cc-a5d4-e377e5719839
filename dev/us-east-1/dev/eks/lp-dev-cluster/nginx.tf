# resource "helm_release" "nginx" {
#   name             = "ingress-nginx"
#   repository       = "https://kubernetes.github.io/ingress-nginx"
#   chart            = "ingress-nginx"
#   version          = "4.6.1"
#   namespace        = "ingress-nginx"
#   create_namespace = true
#   depends_on = [
#     module.eks,
#     module.vpc
#     # helm_release.cilium
#   ]

#   values = [
#     "${file("nginx-values.yaml")}"
#   ]
# }
