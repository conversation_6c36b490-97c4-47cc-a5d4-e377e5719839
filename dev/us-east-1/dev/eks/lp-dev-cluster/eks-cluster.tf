module "eks" {
  source                                = "terraform-aws-modules/eks/aws"
  version                               = "<18"
  cluster_name                          = "${var.cluster_name}-${var.stage}"
  cluster_version                       = "1.27"
  subnets                               = module.vpc.private_subnets
  vpc_id                                = module.vpc.vpc_id
  cluster_endpoint_public_access        = true
  cluster_endpoint_private_access       = true
  cluster_endpoint_private_access_cidrs = module.vpc.private_subnets_cidr_blocks
  enable_irsa                           = true
  map_users                             = [
    { "groups" : ["system:masters"], "userarn" : "arn:aws:iam::************:user/k8s-svc", "username" : "k8s-svc" },
    { "groups" : ["system:masters"], "userarn" : "arn:aws:iam::************:user/qr-dev", "username" : "qr-dev" }
  ]
  map_roles = [
    { "groups" : ["system:bootstrapers", "system:nodes"], "rolearn" : "arn:aws:iam::************:role/KarpenterNodeInstanceProfile-qr-mgmt", "username" : "system:node:{{EC2PrivateDNSName}}" },
    { "groups" : ["system:masters", "system:masters"], "rolearn" : "arn:aws:iam::************:role/qr_dev_role", "username" : "qr-dev" },
    { "groups" : ["system:masters", "system:masters"], "rolearn" : "arn:aws:iam::************:role/OrganizationAccountAccessRole", "username" : "lp-org" }
  ]

  depends_on = [
    # aws_instance.tscale,
    # aws_security_group.tscale_sg,
    # aws_security_group_rule.direct_connections,
    # aws_security_group_rule.allow_all,
    # aws_iam_role_policy_attachment.tscale_ssm-core-attach
  ]

  cluster_encryption_config = [
    {
      provider_key_arn = aws_kms_key.eks_secrets.arn
      resources        = ["secrets"]
    }
  ]

  node_groups = {
    "spot-xlarge" = {
      desired_capacity = 3
      max_capacity     = 5
      min_capacity     = 1

      launch_template_id      = aws_launch_template.lt.id
      launch_template_version = aws_launch_template.lt.default_version

      instance_types = ["t3.large"]
      capacity_type  = "SPOT"
      k8s_labels = {
        environment   = "${var.stage}"
        role          = "general"
        capacity-type = "spot"
      }
      additional_tags = {
        Name = "${var.cluster_name}-blue-node"
      }
    }

    "vault-od-xlarge" = {
      desired_capacity = 2
      max_capacity     = 5
      min_capacity     = 1

      launch_template_id      = aws_launch_template.lt.id
      launch_template_version = aws_launch_template.lt.default_version

      instance_types = ["t3.large"]
      capacity_type  = "ON_DEMAND"
      k8s_labels = {
        environment   = "${var.stage}"
        role          = "vault"
        capacity-type = "spot"
      }

      k8s_taints = {
        key    = "role"
        value  = "vault"
        effect = "NO_SCHEDULE"
      }

      additional_tags = {
        Name = "${var.cluster_name}-vault-node"
      }
    }
  }
}

resource "null_resource" "get_kubeconfig" {
  provisioner "local-exec" {
    command = "aws eks update-kubeconfig --name ${module.eks.cluster_id} --alias ${var.cluster_name} --region=us-east-1"
  }
  depends_on = [
    module.eks
  ]
}
