# resource "null_resource" "rm_aws_node" {
#   provisioner "local-exec" {
#     command = "kubectl config set-context arn:aws:eks:us-east-2:825569692836:cluster/qr-eks-dev && kubectl -n kube-system delete daemonset aws-node"
#   }

#   depends_on = [
#     null_resource.get_kubeconfig
#   ]
# }

resource "null_resource" "argocd" {
  provisioner "local-exec" {
    command = "kubectl create namespace argocd && kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml && kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml"
  }
}
