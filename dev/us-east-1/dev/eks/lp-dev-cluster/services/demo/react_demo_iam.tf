resource "aws_iam_role" "blend_access_role" {
  name               = "demo-role"
  assume_role_policy = templatefile("react_demo_oidc_assume_role_policy.json", { OIDC_ARN = module.eks.oidc_provider_arn, OIDC_URL = replace(module.eks.cluster_oidc_issuer_url, "https://", ""), NAMESPACE = "demo", RD_SA_NAME = "react-demo-sa" })

  depends_on = [module.eks]
}

resource "aws_iam_policy" "blend_access_policy" {
  name        = "DemoAccess"
  path        = "/"
  description = ""
  policy = jsonencode({
    Version = "2012-10-17"
    "Statement" : [
      {
        "Sid" : "VisualEditor0",
        "Effect" : "Allow",
        "Action" : [
          "secretsmanager:GetRandomPassword",
          "kms:*",
          "secretsmanager:ListSecrets"
        ],
        "Resource" : "*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "secretsmanager:GetResourcePolicy",
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret",
          "secretsmanager:ListSecretVersionIds"
        ],
        "Resource" : [
          "arn:aws:secretsmanager:us-west-2:447705907253:secret:demo-*"
        ]
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "secretsmanager:GetRandomPassword",
          "secretsmanager:ListSecrets",
          "kms:*"
        ],
        "Resource" : [
          "*"
        ]
      },
      # {
      #   "Effect" : "Allow",
      #   "Action" : [
      #     "iam:GetRole",
      #     "iam:PassRole",
      #     "iam:GetRolePolicy"
      #   ],
      #   "Resource" : "*"
      # }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "blend_access" {
  role       = aws_iam_role.blend_access_role.name
  policy_arn = aws_iam_policy.blend_access_policy.arn

  depends_on = [aws_iam_role.blend_access_role]
}
