{"apiVersion": "v1", "kind": "Namespace", "metadata": {"annotations": {"cattle.io/status": "{\"Conditions\":[{\"Type\":\"ResourceQuotaInit\",\"Status\":\"True\",\"Message\":\"\",\"LastUpdateTime\":\"2023-01-31T04:07:57Z\"},{\"Type\":\"InitialRolesPopulated\",\"Status\":\"True\",\"Message\":\"\",\"LastUpdateTime\":\"2023-01-31T04:07:57Z\"}]}", "lifecycle.cattle.io/create.namespace-auth": "true"}, "creationTimestamp": "2023-01-31T04:07:56Z", "deletionTimestamp": "2023-01-31T04:12:43Z", "labels": {"kubernetes.io/metadata.name": "loft", "name": "loft"}, "name": "loft", "resourceVersion": "3373783", "uid": "42da81b6-e371-4dd6-b2e9-e9168206fde2"}, "spec": {"finalizers": []}, "status": {"conditions": [{"lastTransitionTime": "2023-01-31T04:12:50Z", "message": "Discovery failed for some groups, 1 failing: unable to retrieve the complete list of server APIs: cluster.loft.sh/v1: the server is currently unable to handle the request", "reason": "DiscoveryFailed", "status": "True", "type": "NamespaceDeletionDiscoveryFailure"}, {"lastTransitionTime": "2023-01-31T04:12:49Z", "message": "All legacy kube types successfully parsed", "reason": "ParsedGroupVersions", "status": "False", "type": "NamespaceDeletionGroupVersionParsingFailure"}, {"lastTransitionTime": "2023-01-31T04:12:50Z", "message": "All content successfully deleted, may be waiting on finalization", "reason": "ContentDeleted", "status": "False", "type": "NamespaceDeletionContentFailure"}, {"lastTransitionTime": "2023-01-31T04:12:51Z", "message": "All content successfully removed", "reason": "ContentRemoved", "status": "False", "type": "NamespaceContentRemaining"}, {"lastTransitionTime": "2023-01-31T04:12:49Z", "message": "All content-preserving finalizers finished", "reason": "ContentHasNoFinalizers", "status": "False", "type": "NamespaceFinalizersRemaining"}], "phase": "Terminating"}}