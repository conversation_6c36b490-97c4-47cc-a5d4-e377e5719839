resource "aws_s3_bucket" "extract_staging" {
  bucket = "qa-extract-s3-bucket-stage"
}

resource "aws_s3_bucket" "extract" {
  bucket = "qa-extract-s3-bucket"
}

resource "aws_athena_database" "extract_db_staging" {
  name   = "qa_extract_db_stage"
  bucket = aws_s3_bucket.extract_staging.id
}

resource "aws_athena_database" "extract_db" {
  name   = "qa_extract_db"
  bucket = aws_s3_bucket.extract.id
}
