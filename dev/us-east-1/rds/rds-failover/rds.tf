module "cluster" {
  source  = "terraform-aws-modules/rds-aurora/aws"

  name           = "qr-failover-test-cluster"
  engine         = "aurora-postgresql"
  engine_version = "13.7"
  instance_class = "db.t3.large"
  instances = {
    one = {}
    2 = {
      instance_class = "db.t3.large"
    }
  }
  create_random_password = false
  master_password = "rtdds1!!"
  master_username = "lpfo"

  vpc_id  = module.vpc.vpc_id
  subnets = module.vpc.public_subnets

  allowed_security_groups = []
  allowed_cidr_blocks     = ["0.0.0.0/0"]
  publicly_accessible = true

  storage_encrypted   = true
  apply_immediately   = true
  monitoring_interval = 10

  db_parameter_group_name         = "fo-db-parameter-group"
  db_cluster_parameter_group_name = "fo-cluster-parameter-group"

  enabled_cloudwatch_logs_exports = ["postgresql"]

  tags = {
    Environment = "dev"
    Terraform   = "true"
  }
}