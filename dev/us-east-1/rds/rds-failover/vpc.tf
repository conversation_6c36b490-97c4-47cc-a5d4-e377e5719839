module "vpc" {
  source = "terraform-aws-modules/vpc/aws"

  name = "qr-rds-failover-vpc"

  cidr = "********/16"

  azs                     = var.azs
  private_subnets         = var.private_subnet_cidrs
  public_subnets          = var.public_subnet_cidrs
  map_public_ip_on_launch = true

  enable_ipv6 = false

  enable_nat_gateway                     = true
  single_nat_gateway                     = true
  enable_dns_hostnames                   = true
  enable_dns_support                     = true
  create_database_subnet_group           = true
  create_database_subnet_route_table     = true
  create_database_internet_gateway_route = true

  public_subnet_tags = {
    Name                                    = "qr-rds-failover-vpc-public"
  }

  private_subnet_tags = {
    Name                                    = "qr-rds-failover-vpc-private"
  }

  tags = {
    Owner       = "QR"
    Environment = "dev"
  }

  vpc_tags = {
    Name = "qr-rds-failover-vpc"
  }
}
