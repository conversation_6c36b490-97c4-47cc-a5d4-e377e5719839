resource "aws_eks_node_group" "base" {
  cluster_name    = data.aws_eks_cluster.lp.name
  node_group_name = "default-standard-base"
  node_role_arn   = "arn:aws:iam::381475384502:role/EKSNodeInstanceRoleVersion2"
  subnet_ids      = ["subnet-06a8db26cec0f37b4", "subnet-019dfbefb4a9c844c", "subnet-05950d9ce094dc6a8", "subnet-09930b05d856e55f1", "subnet-026af7d6b83f9e5d4"]
  instance_types = ["t3.2xlarge", "t3a.2xlarge", "c5.2xlarge"]
  version = "1.22"

  launch_template {
    id      = "lt-09bd8ce9347d83227"
    version = 1
  }

  scaling_config {
    desired_size = 6
    max_size     = 10
    min_size     = 3
  }

  update_config {
    max_unavailable = 1
  }

}

