resource "aws_s3_bucket" "legacy_pg_backup" {
  bucket = "lp-legacy-postgres-backups"
  acl    = "private"

  versioning {
    enabled = true
  }

  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

  tags = {
    Name        = "LP Postgres Legacy Backups"
    Environment = "Legacy"
    Team        = "SWAT"
    Contact     = "<PERSON>"
  }
}

resource "aws_s3_bucket" "cost_reporting" {
  bucket = "lp-cost-reporting"
  acl    = "private"

  versioning {
    enabled = false
  }

  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm = "AES256"
      }
    }
  }

  tags = {
    Name        = "LP Cost and Usage Reporting"
    Environment = "Legacy"
    Team        = "INFRA"
    Contact     = "<PERSON>"
  }
}
