resource "aws_instance" "tscale" {
  ami                         = "ami-0db36bcbb6bf68b98"
  instance_type               = "t4g.medium"
  user_data                   = local.tailscale-userdata
  subnet_id                   = "subnet-081c93bcf836533af"
  associate_public_ip_address = false
  iam_instance_profile        = aws_iam_instance_profile.tscale_profile.name
  vpc_security_group_ids      = [aws_security_group.tscale_sg.id]
  key_name                    = aws_key_pair.keypair.key_name

  tags = {
    Name = "tailscale-router-poc-production"
  }
}

resource "aws_security_group" "tscale_sg" {
  name        = "qr-tailscale-sg"
  description = "tailscale-legacy security group"
  vpc_id      = "vpc-0b25911da3ca9c218"

  tags = {
    Name = "tailscale sg"
  }
}

# resource "aws_security_group_rule" "ssh" {
#   type              = "ingress"
#   from_port         = 22
#   to_port           = 22
#   protocol          = "tcp"
#   cidr_blocks       = ["0.0.0.0/0"]
#   security_group_id = aws_security_group.tscale_sg.id
# }

resource "aws_security_group_rule" "direct_connections" {
  type              = "ingress"
  description       = "Allow UDP direct connections"
  from_port         = 41641
  to_port           = 41641
  protocol          = "udp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.tscale_sg.id
}

resource "aws_security_group_rule" "allow_all" {
  type              = "egress"
  to_port           = 0
  protocol          = "-1"
  from_port         = 0
  security_group_id = aws_security_group.tscale_sg.id
  cidr_blocks       = ["0.0.0.0/0"]
}

resource "aws_iam_instance_profile" "tscale_profile" {
  name = "tailscale_legacy_profile"
  role = aws_iam_role.tscale.name
}

resource "aws_iam_role" "tscale" {
  name = "tailscale_legacy_role"
  path = "/"

  assume_role_policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Action": "sts:AssumeRole",
            "Principal": {
               "Service": "ec2.amazonaws.com"
            },
            "Effect": "Allow",
            "Sid": ""
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "tscale_ssm-core-attach" {
  role       = aws_iam_role.tscale.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}