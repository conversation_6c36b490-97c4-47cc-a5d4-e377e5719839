variable "cluster_name" {
  type    = string
  default = "qr-ec2"
}

variable "namespace" {
  default = ""
}

variable "stage" {
  default = "dev"
}

variable "vpc_cidr" {
  type    = string
  default = "13.0.0.0/16"
}

variable "azs" {
  default = ["us-east-2a", "us-east-2b", "us-east-2c"]
}

variable "public_subnet_cidrs" {
  default = ["13.0.101.0/24"]
}

variable "private_subnets" {
  default = []
}

variable "private_subnet_cidrs" {
  default = ["13.0.104.0/24"]
}

variable "oidc_thumbprint_list" {
  default = []
}

variable "iam_path" {
  default = "/"
}

variable "instance_types" {
  type    = list(string)
  default = ["t3.xlarge"]
}

variable "aws_sbox_access_key" {
  default = ""
}

variable "aws_sbox_secret_key" {
  default = ""
}

variable "docker_username" {
  default = ""
}

variable "docker_password" {
  default = ""
}

variable "infra_gitops_rsa" {
  default = ""
}

variable "http_port" {
  default = 31816
}

variable "https_port" {
  default = 31046
}