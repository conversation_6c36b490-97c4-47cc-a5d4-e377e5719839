resource "aws_instance" "pg_backups" {
  ami                    = "ami-0b0dcb5067f052a63"
  instance_type          = "c4.2xlarge"
  user_data              = local.pg-backup-userdata
  subnet_id              = "subnet-09c3667f"
  iam_instance_profile   = aws_iam_instance_profile.pg_backups_legacy_profile.name
  vpc_security_group_ids = [aws_security_group.pg_backups_legacy_sg.id]
  key_name               = aws_key_pair.keypair.key_name

  root_block_device {
    # encrypted = true
    # kms_key_id = aws_kms_key.dev_ebs_encryption.key_id
    volume_size = 500
  }

  tags = {
    Name = "QR Postgres backup instance"
  }
}

resource "aws_security_group" "pg_backups_legacy_sg" {
  name        = "pg-backups-legacy"
  description = "pg-backups security group"
  vpc_id      = "vpc-02c6b366"

  tags = {
    Name = "PG Backups SG"
  }
}

resource "aws_security_group_rule" "pg_backups_legacy_ssh" {
  type              = "ingress"
  from_port         = 22
  to_port           = 22
  protocol          = "tcp"
  cidr_blocks       = ["**************/32"]
  security_group_id = aws_security_group.pg_backups_legacy_sg.id
}

resource "aws_security_group_rule" "pg_backups_legacy_allow_all" {
  type              = "egress"
  to_port           = 0
  protocol          = "-1"
  from_port         = 0
  security_group_id = aws_security_group.pg_backups_legacy_sg.id
  cidr_blocks       = ["0.0.0.0/0"]
}

resource "aws_iam_instance_profile" "pg_backups_legacy_profile" {
  name = "pg_backups_legacy_profile"
  role = aws_iam_role.pg_backups_legacy.name
}

resource "aws_iam_role" "pg_backups_legacy" {
  name = "pg_backups_legacy_role"
  path = "/"

  assume_role_policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Action": "sts:AssumeRole",
            "Principal": {
               "Service": "ec2.amazonaws.com"
            },
            "Effect": "Allow",
            "Sid": ""
        }
    ]
}
EOF
}

resource "aws_iam_policy" "pg_s3_access_policy" {
  name        = "pg-backups-s3-policy"
  path        = "/"
  description = "Policy for storing PG backups in S3"

  # Terraform's "jsonencode" function converts a
  # Terraform expression result to valid JSON syntax.
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "s3:*"
        Effect   = "Allow"
        Resource = "arn:aws:s3:::lp-legacy-postgres-backups"
      },
    ]
  })
}

resource "aws_iam_role_policy_attachment" "pg_backups_legacy_ssm-core-attach" {
  role       = aws_iam_role.pg_backups_legacy.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_role_policy_attachment" "admin-attach" {
  role       = aws_iam_role.pg_backups_legacy.name
  policy_arn = aws_iam_policy.pg_s3_access_policy.arn
}